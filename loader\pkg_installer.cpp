// Copyright 2025 <Copyright Owner>

#include "pkg_installer.h"
#include "../ps4/ps4_emulator.h"
#include "../ps4/ps4_filesystem.h"

#define NOMINMAX
#include <openssl/aes.h>
#include <openssl/sha.h>
#ifdef _WIN32
#include <wincrypt.h>
#include <windows.h>
#else
#include <iconv.h>
#endif

#include <algorithm>
#include <cassert>
#include <cctype>
#include <cstring>
#include <filesystem>
#include <iomanip>
#include <iostream>
#include <mutex>
#include <sstream>
#include <thread>

// Placeholder key management (replace with secure key storage in production)
namespace KeyManager {
static const std::vector<uint8_t> kPlaceholderKey = {
    0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0A,
    0x0B, 0x0C, 0x0D, 0x0E, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14, 0x15,
    0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C, 0x1D, 0x1E, 0x1F};
static const std::vector<uint8_t> kPlaceholderIV = {
    0xFF, 0xFE, 0xFD, 0xFC, 0xFB, 0xFA, 0xF9, 0xF8,
    0xF7, 0xF6, 0xF5, 0xF4, 0xF3, 0xF2, 0xF1, 0xF0};

const std::vector<uint8_t> &GetPKGKey(uint32_t keyIndex) {
  // Implement secure key retrieval based on keyIndex
  // This should be replaced with actual PS4 PKG decryption keys in production
  static std::unordered_map<uint32_t, std::vector<uint8_t>> keyDatabase;

  // Initialize with placeholder keys for different indices
  if (keyDatabase.empty()) {
    // Sample keys for different PKG types (these are not real PS4 keys)
    keyDatabase[0] = kPlaceholderKey;
    keyDatabase[1] = {0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
                      0x18, 0x19, 0x1A, 0x1B, 0x1C, 0x1D, 0x1E, 0x1F,
                      0x20, 0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27,
                      0x28, 0x29, 0x2A, 0x2B, 0x2C, 0x2D, 0x2E, 0x2F};
    keyDatabase[2] = {0x30, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37,
                      0x38, 0x39, 0x3A, 0x3B, 0x3C, 0x3D, 0x3E, 0x3F,
                      0x40, 0x41, 0x42, 0x43, 0x44, 0x45, 0x46, 0x47,
                      0x48, 0x49, 0x4A, 0x4B, 0x4C, 0x4D, 0x4E, 0x4F};
  }

  auto it = keyDatabase.find(keyIndex);
  if (it != keyDatabase.end()) {
    spdlog::debug("Using key for index {}", keyIndex);
    return it->second;
  }

  spdlog::warn("Key not found for index {}, using placeholder key", keyIndex);
  return kPlaceholderKey;
}

const std::vector<uint8_t> &GetPKGIV(uint32_t keyIndex) {
  // Implement secure IV retrieval
  static std::unordered_map<uint32_t, std::vector<uint8_t>> ivDatabase;

  if (ivDatabase.empty()) {
    ivDatabase[0] = kPlaceholderIV;
    ivDatabase[1] = {0xE0, 0xE1, 0xE2, 0xE3, 0xE4, 0xE5, 0xE6, 0xE7,
                     0xE8, 0xE9, 0xEA, 0xEB, 0xEC, 0xED, 0xEE, 0xEF};
    ivDatabase[2] = {0xD0, 0xD1, 0xD2, 0xD3, 0xD4, 0xD5, 0xD6, 0xD7,
                     0xD8, 0xD9, 0xDA, 0xDB, 0xDC, 0xDD, 0xDE, 0xDF};
  }

  auto it = ivDatabase.find(keyIndex);
  if (it != ivDatabase.end()) {
    spdlog::debug("Using IV for index {}", keyIndex);
    return it->second;
  }

  spdlog::warn("IV not found for index {}, using placeholder IV", keyIndex);
  return kPlaceholderIV;
}
} // namespace KeyManager

// Byte order conversion utilities
class PKGEndian {
public:
  static uint16_t be16toh(uint16_t val) {
#ifdef _WIN32
    return _byteswap_ushort(val);
#else
    return __builtin_bswap16(val);
#endif
  }

  static uint32_t be32toh(uint32_t val) {
#ifdef _WIN32
    return _byteswap_ulong(val);
#else
    return __builtin_bswap32(val);
#endif
  }

  static uint64_t be64toh(uint64_t val) {
#ifdef _WIN32
    return _byteswap_uint64(val);
#else
    return __builtin_bswap64(val);
#endif
  }
};

// SFO (System File Object) constants
#define SFO_MAGIC_INTERNAL 0x00505346 // "PSF\0" in little-endian

// Helper function to detect if data is likely binary
bool IsBinaryData(const std::vector<uint8_t> &data) {
  if (data.empty())
    return false;

  int non_printable_count = 0;
  int total_chars = std::min(static_cast<int>(data.size()), 100);

  for (int i = 0; i < total_chars; i++) {
    unsigned char c = data[i];
    if (c < 32 && c != '\t' && c != '\n' && c != '\r') {
      non_printable_count++;
    }
  }

  return (non_printable_count * 10 > total_chars);
}

// Helper function overload for string
bool IsBinaryData(const std::string &data) {
  std::vector<uint8_t> vec(data.begin(), data.end());
  return IsBinaryData(vec);
}

// Helper function to convert Shift-JIS to UTF-8 (non-Windows)
#ifndef _WIN32
std::string ConvertShiftJISToUTF8(const std::string &input) {
  iconv_t cd = iconv_open("UTF-8", "SHIFT-JIS");
  if (cd == (iconv_t)-1) {
    spdlog::error("Failed to initialize iconv for Shift-JIS to UTF-8");
    return input;
  }

  std::string output;
  size_t in_bytes = input.size();
  size_t out_bytes = in_bytes * 4; // UTF-8 can be up to 4 bytes per char
  std::vector<char> out_buf(out_bytes);
  char *in_ptr = const_cast<char *>(input.data());
  char *out_ptr = out_buf.data();
  size_t out_bytes_left = out_bytes;

  if (iconv(cd, &in_ptr, &in_bytes, &out_ptr, &out_bytes_left) == (size_t)-1) {
    spdlog::warn("Failed to convert Shift-JIS to UTF-8: {}", strerror(errno));
    iconv_close(cd);
    return input;
  }

  output.assign(out_buf.data(), out_buf.size() - out_bytes_left);
  iconv_close(cd);
  return output;
}
#else
std::string ConvertShiftJISToUTF8(const std::string &input) {
  // Placeholder for Windows (implement using
  // MultiByteToWideChar/WideCharToMultiByte)
  spdlog::warn("Shift-JIS conversion not implemented on Windows");
  return input;
}
#endif

// System file ID mapping for ID-based references
static const std::unordered_map<uint32_t, std::string> kSystemFileIDs = {
    {0x0001, "eboot.bin"}, {0x0002, "param.sfo"}, {0x0003, "icon0.png"},
    {0x0004, "pic0.png"},  {0x0005, "snd0.at9"},
    // Add more as needed
};

namespace ps4 {

PKGInstaller::PKGInstaller(PS4Filesystem *filesystem, PS4Emulator *emulator)
    : m_filesystem(filesystem), m_emulator(emulator) {
  if (!filesystem) {
    throw PKGException("PKGInstaller requires a valid PS4Filesystem");
  }

  // Use the correct installation path that matches where the game loader expects files
  m_installRoot = "/mnt/sandbox/pfsmnt";

  // Load previously installed packages
  LoadInstalledPackages();

  spdlog::info("PKGInstaller initialized with filesystem, install root: {}", m_installRoot);
}

PKGInstaller::~PKGInstaller() = default;

bool PKGInstaller::InstallPKG(const std::string &pkgPath,
                              const std::string &installPath) {
  std::lock_guard<std::mutex> lock(m_installMutex);

  spdlog::info("Installing PKG: {} to {}", pkgPath, installPath);

  try {
    if (!ValidatePKG(pkgPath)) {
      spdlog::error("PKG validation failed - not a valid PS4 PKG file");
      return false;
    }

    std::ifstream file(pkgPath, std::ios::binary);
    if (!file.is_open()) {
      spdlog::error("Failed to open PKG file: {}", pkgPath);
      return false;
    }

    PKGHeader header;
    if (!ReadPKGHeader(file, header)) {
      spdlog::error("Invalid PKG header in file: {}", pkgPath);
      return false;
    }
    spdlog::info("PKG Header - Magic: 0x{:08X}, Type: {}, Files: {}, Entries: "
                 "{}, Content Size: {}",
                 header.magic, header.type, header.file_count,
                 header.entry_count, header.content_size);

    std::vector<PKGItem> items;
    if (!ReadPKGItems(file, header, items)) {
      spdlog::error("Failed to read PKG items");
      return false;
    }

    std::vector<uint8_t> nameTable;
    if (!ReadNameTable(file, header, nameTable)) {
      spdlog::error("Failed to read PKG name table");
      return false;
    }

    std::string contentId(
        header.content_id,
        strnlen(header.content_id, sizeof(header.content_id)));
    std::string fullInstallPath =
        CreateInstallDirectory(installPath, contentId);

    std::unordered_map<std::string, std::vector<uint8_t>> extractedFiles;
    int validItemCount = 0;

    for (size_t i = 0; i < items.size(); i++) {
      const PKGItem &item = items[i];
      std::vector<uint8_t> itemData;
      if (!ExtractPKGItem(file, item, nameTable, itemData)) {
        spdlog::warn("Failed to extract PKG item {} (offset: 0x{:08X})", i,
                     item.dataOffset);
        continue;
      }

      if (itemData.empty()) {
        spdlog::debug("Skipping empty item {}", i);
        continue;
      }

      std::string itemName =
          ExtractSafeFilename(item, nameTable, static_cast<int>(i));
      std::string uniqueName = itemName;
      int counter = 1;
      while (extractedFiles.find(uniqueName) != extractedFiles.end()) {
        size_t dot_pos = itemName.rfind('.');
        if (dot_pos != std::string::npos) {
          uniqueName = itemName.substr(0, dot_pos) + "_" +
                       std::to_string(counter) + itemName.substr(dot_pos);
        } else {
          uniqueName = itemName + "_" + std::to_string(counter);
        }
        counter++;
      }

      extractedFiles[uniqueName] = std::move(itemData);
      validItemCount++;
      spdlog::debug("Extracted item {}: {} ({} bytes)", i, uniqueName,
                    itemData.size());
    }

    spdlog::info("Successfully extracted {} valid items from PKG",
                 validItemCount);

    int installedCount = 0;
    for (const auto &[filename, data] : extractedFiles) {
      std::string virtualPath = fullInstallPath + "/" + filename;
      std::replace(virtualPath.begin(), virtualPath.end(), '\\', '/');
      while (virtualPath.find("//") != std::string::npos) {
        virtualPath.replace(virtualPath.find("//"), 2, "/");
      }

      std::string parentPath =
          virtualPath.substr(0, virtualPath.find_last_of('/'));
      m_filesystem->CreateVirtualDirectory(parentPath);

      // Retry write up to 3 times
      bool writeSuccess = false;
      for (int attempt = 1; attempt <= 3; ++attempt) {
        if (m_filesystem->WriteFile(virtualPath, data.data(), data.size())) {
          writeSuccess = true;
          break;
        }
        spdlog::warn("Write attempt {}/3 failed for: {}", attempt, virtualPath);
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
      }

      if (!writeSuccess) {
        spdlog::error("Failed to write file to virtual filesystem: {}",
                      virtualPath);
        continue;
      }

      installedCount++;
      spdlog::info("Installed file: {}", virtualPath);
    }

    // Note: m_installMutex is already held by the outer lock_guard
    m_installedPackages[contentId] = fullInstallPath;

    // Save the updated installed packages list to disk
    SaveInstalledPackages();

    spdlog::info(
        "Successfully installed PKG: {} (Content ID: {}) - {} files installed",
        pkgPath, contentId, installedCount);
    return installedCount > 0;

  } catch (const std::exception &e) {
    spdlog::error("Exception during PKG installation: {}", e.what());
    return false;
  }
}

bool PKGInstaller::InstallUpdatePKG(const std::string &updatePkgPath,
                                    const std::string &baseGamePath) {
  spdlog::info("Installing update PKG: {} for base game: {}", updatePkgPath,
               baseGamePath);

  try {
    std::ifstream file(updatePkgPath, std::ios::binary);
    if (!file.is_open()) {
      spdlog::error("Failed to open update PKG file: {}", updatePkgPath);
      return false;
    }

    PKGHeader header;
    if (!ReadPKGHeader(file, header)) {
      spdlog::error("Invalid update PKG header");
      return false;
    }

    if (header.type != PKG_TYPE_UPDATE) {
      spdlog::error("PKG is not an update package (type: {})", header.type);
      return false;
    }

    std::vector<PKGItem> items;
    if (!ReadPKGItems(file, header, items)) {
      spdlog::error("Failed to read update PKG items");
      return false;
    }

    std::vector<uint8_t> nameTable;
    if (!ReadNameTable(file, header, nameTable)) {
      spdlog::error("Failed to read update PKG name table");
      return false;
    }

    std::unordered_map<std::string, std::vector<uint8_t>> updateFiles;
    for (size_t i = 0; i < items.size(); i++) {
      const PKGItem &item = items[i];
      std::vector<uint8_t> itemData;
      if (!ExtractPKGItem(file, item, nameTable, itemData)) {
        spdlog::warn("Failed to extract update item {} (offset: 0x{:08X})", i,
                     item.dataOffset);
        continue;
      }

      std::string itemName =
          ExtractSafeFilename(item, nameTable, static_cast<int>(i));
      if (itemName.empty()) {
        spdlog::warn("Skipping update item {} with empty name", i);
        continue;
      }

      updateFiles[itemName] = std::move(itemData);
      spdlog::debug("Extracted update item: {} ({} bytes)", itemName,
                    itemData.size());
    }

    std::unordered_map<std::string, std::vector<uint8_t>> baseFiles;
    std::vector<std::string> baseFilePaths =
        m_filesystem->ListFiles(baseGamePath, true);
    for (const auto &path : baseFilePaths) {
      std::vector<uint8_t> fileData;
      if (m_filesystem->ReadFile(path, fileData)) {
        std::string relativePath = path.substr(baseGamePath.size() + 1);
        baseFiles[relativePath] = std::move(fileData);
        spdlog::debug("Loaded base game file: {} ({} bytes)", relativePath,
                      fileData.size());
      } else {
        spdlog::warn("Failed to read base game file: {}", path);
      }
    }

    if (!MergeUpdateFiles(baseFiles, updateFiles)) {
      spdlog::error("Failed to merge update files");
      return false;
    }

    for (const auto &[filename, data] : baseFiles) {
      std::string virtualPath = baseGamePath + "/" + filename;
      if (!m_filesystem->WriteFile(virtualPath, data.data(), data.size())) {
        spdlog::error("Failed to write updated file: {}", virtualPath);
        continue;
      }
      spdlog::debug("Updated file: {}", virtualPath);
    }

    spdlog::info("Successfully installed update PKG");
    return true;

  } catch (const std::exception &e) {
    spdlog::error("Exception during update PKG installation: {}", e.what());
    return false;
  }
}

bool PKGInstaller::ValidatePKG(const std::string &pkgPath) {
  spdlog::info("Validating PKG: {}", pkgPath);

  std::ifstream file(pkgPath, std::ios::binary | std::ios::ate);
  if (!file.is_open()) {
    spdlog::error("Failed to open PKG file: {}", pkgPath);
    return false;
  }

  uint64_t fileSize = file.tellg();
  file.seekg(0, std::ios::beg);

  PKGHeader header;
  if (!ReadPKGHeader(file, header)) {
    spdlog::error("Invalid PKG header");
    return false;
  }

  if (header.magic != PKG_MAGIC) {
    spdlog::error("Invalid PKG magic number: 0x{:08X}", header.magic);
    return false;
  }

  if (header.entry_count == 0 || header.entry_count > 100'000) {
    spdlog::error("Invalid entry count: {}", header.entry_count);
    return false;
  }

  if (header.file_count == 0 || header.file_count > header.entry_count) {
    spdlog::error("Invalid file count: {} (entry count: {})", header.file_count,
                  header.entry_count);
    return false;
  }

  if (header.table_offset == 0 || header.table_offset >= fileSize) {
    spdlog::error("Invalid table offset: 0x{:08X}", header.table_offset);
    return false;
  }

  if (header.body_offset == 0 || header.body_offset >= fileSize) {
    spdlog::error("Invalid body offset: 0x{:08X}", header.body_offset);
    return false;
  }

  if (header.content_size == 0) {
    spdlog::warn("Content size is zero, using body size ({}) as fallback",
                 header.body_size);
    header.content_size = header.body_size;
    header.content_offset = header.body_offset;
  }

  if (header.content_offset >= fileSize ||
      header.content_size > fileSize - header.content_offset) {
    spdlog::error("Invalid content section: offset=0x{:08X}, size={}",
                  header.content_offset, header.content_size);
    return false;
  }

  std::vector<uint8_t> packageData(fileSize);
  file.seekg(0, std::ios::beg);
  file.read(reinterpret_cast<char *>(packageData.data()), fileSize);
  if (!file.good()) {
    spdlog::error("Failed to read PKG file for integrity check");
    return false;
  }

  if (!VerifyPackageIntegrity(header, packageData)) {
    spdlog::error("Package integrity verification failed");
    return false;
  }

  spdlog::info("PKG validation passed");
  return true;
}

bool PKGInstaller::ExtractPKG(const std::string &pkgPath,
                              const std::string &extractPath) {
  spdlog::info("Extracting PKG: {} to {}", pkgPath, extractPath);

  try {
    std::filesystem::create_directories(extractPath);

    std::ifstream file(pkgPath, std::ios::binary);
    if (!file.is_open()) {
      spdlog::error("Failed to open PKG file: {}", pkgPath);
      return false;
    }

    PKGHeader header;
    if (!ReadPKGHeader(file, header)) {
      spdlog::error("Invalid PKG header");
      return false;
    }

    std::vector<PKGItem> items;
    if (!ReadPKGItems(file, header, items)) {
      spdlog::error("Failed to read PKG items");
      return false;
    }

    std::vector<uint8_t> nameTable;
    if (!ReadNameTable(file, header, nameTable)) {
      spdlog::error("Failed to read PKG name table");
      return false;
    }

    for (size_t i = 0; i < items.size(); i++) {
      const PKGItem &item = items[i];
      std::vector<uint8_t> itemData;
      if (!ExtractPKGItem(file, item, nameTable, itemData)) {
        spdlog::warn("Failed to extract item {} (offset: 0x{:08X})", i,
                     item.dataOffset);
        continue;
      }

      std::string itemName =
          ExtractSafeFilename(item, nameTable, static_cast<int>(i));
      if (itemName.empty()) {
        spdlog::warn("Skipping item {} with empty name", i);
        continue;
      }

      std::string outputPath = extractPath + "/" + itemName;
      std::filesystem::create_directories(
          std::filesystem::path(outputPath).parent_path());

      std::ofstream outFile(outputPath, std::ios::binary);
      if (outFile.is_open()) {
        outFile.write(reinterpret_cast<const char *>(itemData.data()),
                      itemData.size());
        outFile.close();
        spdlog::debug("Extracted: {}", outputPath);
      } else {
        spdlog::error("Failed to write extracted file: {}", outputPath);
      }
    }

    spdlog::info("Successfully extracted PKG to: {}", extractPath);
    return true;

  } catch (const std::exception &e) {
    spdlog::error("Exception during PKG extraction: {}", e.what());
    return false;
  }
}

bool PKGInstaller::GetPKGInfo(const std::string &pkgPath,
                              std::string &contentId, std::string &version,
                              std::string &title) {
  std::ifstream file(pkgPath, std::ios::binary);
  if (!file.is_open()) {
    spdlog::error("Failed to open PKG file: {}", pkgPath);
    return false;
  }

  PKGHeader header;
  if (!ReadPKGHeader(file, header)) {
    spdlog::error("Invalid PKG header");
    return false;
  }

  contentId = std::string(
      header.content_id, strnlen(header.content_id, sizeof(header.content_id)));

  std::vector<PKGItem> items;
  if (!ReadPKGItems(file, header, items)) {
    spdlog::error("Failed to read PKG items");
    return false;
  }

  std::vector<uint8_t> nameTable;
  if (!ReadNameTable(file, header, nameTable)) {
    spdlog::error("Failed to read PKG name table");
    return false;
  }

  for (size_t i = 0; i < items.size(); i++) {
    const PKGItem &item = items[i];
    std::string itemName =
        ExtractSafeFilename(item, nameTable, static_cast<int>(i));
    if (itemName == "param.sfo") {
      spdlog::debug("Found param.sfo item at offset 0x{:08X}, size {}", item.dataOffset, item.dataSize);
      std::vector<uint8_t> sfoData;
      if (ExtractPKGItem(file, item, nameTable, sfoData)) {
        spdlog::debug("Successfully extracted param.sfo, size: {}", sfoData.size());
        std::unordered_map<std::string, std::string> sfoParams;
        if (ParseSFO(sfoData, sfoParams)) {
          version = sfoParams["APP_VER"];
          title = sfoParams["TITLE"];
          spdlog::debug("Parsed SFO: version='{}', title='{}'", version, title);
        } else {
          spdlog::error("Failed to parse SFO data");
        }
      } else {
        spdlog::error("Failed to extract param.sfo");
      }
      break;
    }
  }

  return true;
}

std::vector<std::string> PKGInstaller::ListInstalledPackages() const {
  std::vector<std::string> packages;
  std::lock_guard<std::mutex> lock(const_cast<std::mutex &>(m_installMutex));
  packages.reserve(m_installedPackages.size());

  for (const auto &[contentId, path] : m_installedPackages) {
    packages.push_back(contentId);
  }

  return packages;
}

bool PKGInstaller::UninstallPackage(const std::string &contentId) {
  std::lock_guard<std::mutex> lock(m_installMutex);
  auto it = m_installedPackages.find(contentId);
  if (it == m_installedPackages.end()) {
    spdlog::error("Package not found: {}", contentId);
    return false;
  }

  spdlog::info("Uninstalling package: {} from {}", contentId, it->second);
  // Implement recursive deletion in PS4Filesystem
  bool success = m_filesystem->RemoveDirectory(it->second);
  if (success) {
    m_installedPackages.erase(it);
    spdlog::info("Successfully uninstalled package: {}", contentId);
  } else {
    spdlog::error("Failed to remove package directory: {}", it->second);
  }
  return success;
}

bool PKGInstaller::ReadPKGHeader(std::ifstream &file, PKGHeader &header) {
  file.seekg(0, std::ios::beg);
  char raw_header[sizeof(PKGHeader)];
  file.read(raw_header, sizeof(PKGHeader));

  if (!file.good()) {
    spdlog::error("Failed to read PKG header - file I/O error");
    return false;
  }

  std::memcpy(&header, raw_header, sizeof(PKGHeader));

  uint32_t magic_raw = header.magic;
  header.magic = PKGEndian::be32toh(header.magic);
  header.type = PKGEndian::be32toh(header.type);
  header.unknown_0x008 = PKGEndian::be32toh(header.unknown_0x008);
  header.file_count = PKGEndian::be32toh(header.file_count);
  header.entry_count = PKGEndian::be32toh(header.entry_count);
  header.sc_entry_count = PKGEndian::be16toh(header.sc_entry_count);
  header.entry_count_2 = PKGEndian::be16toh(header.entry_count_2);
  header.table_offset = PKGEndian::be32toh(header.table_offset);
  header.entry_data_size = PKGEndian::be32toh(header.entry_data_size);
  header.body_offset = PKGEndian::be64toh(header.body_offset);
  header.body_size = PKGEndian::be64toh(header.body_size);
  header.content_offset = PKGEndian::be64toh(header.content_offset);
  header.content_size = PKGEndian::be64toh(header.content_size);
  header.drm_type = PKGEndian::be32toh(header.drm_type);
  header.content_type = PKGEndian::be32toh(header.content_type);
  header.content_flags = PKGEndian::be32toh(header.content_flags);
  header.promote_size = PKGEndian::be32toh(header.promote_size);
  header.version_date = PKGEndian::be32toh(header.version_date);
  header.version_hash = PKGEndian::be32toh(header.version_hash);
  header.unknown_0x088 = PKGEndian::be32toh(header.unknown_0x088);
  header.unknown_0x08C = PKGEndian::be32toh(header.unknown_0x08C);
  header.iro_tag = PKGEndian::be64toh(header.iro_tag);
  header.drm_type_version = PKGEndian::be32toh(header.drm_type_version);

  if (header.magic != PKG_MAGIC) {
    spdlog::error("Invalid PKG magic number: got 0x{:08X} (raw: 0x{:08X}), "
                  "expected 0x{:08X}",
                  header.magic, magic_raw, PKG_MAGIC);
    if (magic_raw == 0x504B0304) {
      spdlog::error("File appears to be a ZIP archive (magic: 'PK')");
    } else if (magic_raw == 0x7F454C46) {
      spdlog::error("File appears to be an ELF executable");
    } else if ((magic_raw & 0xFFFF0000) == 0x4D5A0000) {
      spdlog::error("File appears to be a Windows PE executable");
    }
    return false;
  }

  if (header.entry_count == 0 || header.entry_count > 100'000) {
    spdlog::error("Invalid entry count: {} (should be 1-100000)",
                  header.entry_count);
    return false;
  }

  if (header.file_count == 0 || header.file_count > header.entry_count) {
    spdlog::error("Invalid file count: {} (entry count: {})", header.file_count,
                  header.entry_count);
    return false;
  }

  if (header.table_offset == 0 || header.table_offset >= 0x100000000ULL) {
    spdlog::error("Invalid table offset: 0x{:08X}", header.table_offset);
    return false;
  }

  spdlog::info("PKG Header parsed successfully:");
  spdlog::info("  Magic: 0x{:08X}", header.magic);
  spdlog::info("  Type: 0x{:08X}", header.type);
  spdlog::info("  File Count: {}", header.file_count);
  spdlog::info("  Entry Count: {}", header.entry_count);
  spdlog::info("  Table Offset: 0x{:08X}", header.table_offset);
  spdlog::info(
      "  Content ID: {}",
      std::string(header.content_id, std::min(static_cast<size_t>(0x24),
                                              strlen(header.content_id))));

  return true;
}

bool PKGInstaller::ReadPKGItems(std::ifstream &file, const PKGHeader &header,
                                std::vector<PKGItem> &items) {
  if (header.entry_count == 0) {
    spdlog::warn("PKG has no entries");
    return true;
  }

  std::vector<PKGTableEntry> table_entries(header.entry_count);
  file.seekg(header.table_offset, std::ios::beg);
  if (!file.good()) {
    spdlog::error("Failed to seek to PKG table at offset 0x{:08X}",
                  header.table_offset);
    return false;
  }

  file.read(reinterpret_cast<char *>(table_entries.data()),
            sizeof(PKGTableEntry) * header.entry_count);
  if (!file.good()) {
    spdlog::error("Failed to read PKG table entries");
    return false;
  }

  items.clear();
  items.reserve(header.entry_count);

  for (const auto &entry : table_entries) {
    PKGItem item;
    item.nameOffset = PKGEndian::be32toh(entry.filename_offset);
    item.nameSize = 0; // Will be calculated later
    item.dataOffset = PKGEndian::be32toh(entry.offset);
    item.dataSize = PKGEndian::be32toh(entry.size);
    item.flags = static_cast<uint8_t>(PKGEndian::be32toh(entry.flags1) & 0xFF);
    item.type = static_cast<uint8_t>(PKGEndian::be32toh(entry.id) &
                                     0xFF); // Store ID as type
    memset(item.reserved, 0, sizeof(item.reserved));

    if (item.dataOffset == 0 && item.dataSize > 0) {
      spdlog::warn("PKG entry has size {} but no offset", item.dataSize);
      continue;
    }

    items.push_back(item);
  }

  spdlog::info("Successfully read {} PKG table entries", items.size());
  return true;
}

bool PKGInstaller::ReadNameTable(std::ifstream &file, const PKGHeader &header,
                                 std::vector<uint8_t> &nameTable) {
  spdlog::info("Reading PKG name table at offset 0x{:08X}",
               header.table_offset);

  uint64_t nameTableOffset =
      header.table_offset + (sizeof(PKGTableEntry) * header.entry_count);
  uint64_t maxPossibleSize = header.body_offset - nameTableOffset;
  uint32_t nameTableSize = header.entry_data_size;

  if (nameTableSize == 0) {
    spdlog::warn("Name table size is zero, using default size of 4096 bytes");
    nameTableSize = 4096;
  } else if (nameTableSize > maxPossibleSize) {
    spdlog::warn(
        "Name table size ({}) exceeds available space ({}), truncating",
        nameTableSize, maxPossibleSize);
    nameTableSize = static_cast<uint32_t>(maxPossibleSize);
  } else if (nameTableSize > 1'000'000) {
    spdlog::error("Name table size ({}) is unreasonably large, rejecting",
                  nameTableSize);
    return false;
  }

  nameTable.resize(nameTableSize);
  file.seekg(nameTableOffset, std::ios::beg);

  if (!file.good()) {
    spdlog::error("Failed to seek to name table at offset 0x{:08X}",
                  nameTableOffset);
    std::fill(nameTable.begin(), nameTable.end(), 0);
    return false;
  }

  file.read(reinterpret_cast<char *>(nameTable.data()), nameTableSize);
  std::streamsize bytesRead = file.gcount();

  if (bytesRead < static_cast<std::streamsize>(nameTableSize)) {
    spdlog::warn("Incomplete name table read: got {} bytes, expected {}",
                 bytesRead, nameTableSize);
    std::fill(nameTable.begin() + bytesRead, nameTable.end(), 0);
  }

  bool hasValidData = false;
  for (size_t i = 0; i < nameTable.size(); ++i) {
    if (nameTable[i] != 0 &&
        (std::isprint(nameTable[i]) || nameTable[i] >= 0x80)) {
      hasValidData = true;
      break;
    }
  }

  if (!hasValidData) {
    spdlog::warn("Name table contains no valid data, initializing with zeros");
    std::fill(nameTable.begin(), nameTable.end(), 0);
  }

  spdlog::info("Successfully read name table ({} bytes)", bytesRead);
  return true;
}

bool PKGInstaller::ExtractPKGItem(std::ifstream &file, const PKGItem &item,
                                  const std::vector<uint8_t> &nameTable,
                                  std::vector<uint8_t> &data) {
  spdlog::info("Extracting PKG item at offset 0x{:08X}, size {}",
               item.dataOffset, item.dataSize);

  if (item.dataSize == 0) {
    spdlog::warn("Skipping item with zero size");
    return false;
  }

  data.resize(item.dataSize);
  file.seekg(item.dataOffset, std::ios::beg);
  if (!file.good()) {
    spdlog::error("Failed to seek to item offset 0x{:08X}", item.dataOffset);
    return false;
  }

  file.read(reinterpret_cast<char *>(data.data()), item.dataSize);
  if (!file.good()) {
    spdlog::error("Failed to read item data (size: {})", item.dataSize);
    data.clear();
    return false;
  }

  if (item.flags & 0x01) {
    spdlog::info("Encrypted PKG item detected (flags: 0x{:02X})", item.flags);
    // Decrypt using AES-256-CBC
    const auto &key =
        KeyManager::GetPKGKey(item.flags & 0xFE); // Use flags2 for key index
    const auto &iv = KeyManager::GetPKGIV(item.flags & 0xFE);

    if (key.size() != 32 || iv.size() != 16) {
      spdlog::error("Invalid encryption key or IV size");
      data.clear();
      return false;
    }

    std::vector<uint8_t> decryptedData(item.dataSize);
    AES_KEY aesKey;
    AES_set_decrypt_key(key.data(), 256, &aesKey);

    int outLen = 0;
    AES_cbc_encrypt(data.data(), decryptedData.data(), item.dataSize, &aesKey,
                    const_cast<uint8_t *>(iv.data()), AES_DECRYPT);

    // Verify padding (PKCS7)
    uint8_t padByte = decryptedData[item.dataSize - 1];
    if (padByte > 16 || padByte == 0) {
      spdlog::error("Invalid PKCS7 padding after decryption");
      data.clear();
      return false;
    }

    for (size_t i = item.dataSize - padByte; i < item.dataSize; ++i) {
      if (decryptedData[i] != padByte) {
        spdlog::error("Invalid PKCS7 padding bytes");
        data.clear();
        return false;
      }
    }

    decryptedData.resize(item.dataSize - padByte);
    data = std::move(decryptedData);
    spdlog::info("Successfully decrypted item (size: {})", data.size());
  }

  std::string itemHash = ComputeSHA256(data);
  spdlog::debug("Extracted item hash: {}", itemHash);
  spdlog::info("Successfully extracted item (offset: 0x{:08X}, size: {})",
               item.dataOffset, data.size());
  return true;
}

std::string PKGInstaller::GetItemName(const std::vector<uint8_t> &nameTable,
                                      uint32_t nameOffset, uint32_t nameSize) {
  if (nameOffset >= nameTable.size()) {
    spdlog::warn("Invalid name offset: {} (name table size: {})", nameOffset,
                 nameTable.size());
    return "";
  }

  size_t maxLength = nameTable.size() - nameOffset;
  size_t length = nameSize;

  if (length == 0) {
    length =
        strnlen(reinterpret_cast<const char *>(nameTable.data() + nameOffset),
                maxLength);
  } else if (nameOffset + length > nameTable.size()) {
    spdlog::warn("Name offset + size exceeds name table: offset={}, size={}",
                 nameOffset, length);
    length = maxLength;
  }

  if (length == 0) {
    spdlog::warn("Empty name at offset {}", nameOffset);
    return "";
  }

  std::string rawName(
      reinterpret_cast<const char *>(nameTable.data() + nameOffset), length);

  // Validate UTF-8
  bool isValidUtf8 = true;
  for (size_t i = 0; i < rawName.size(); ++i) {
    unsigned char c = rawName[i];
    if (c >= 0x80) {
      int bytes = 0;
      if ((c & 0xE0) == 0xC0)
        bytes = 2;
      else if ((c & 0xF0) == 0xE0)
        bytes = 3;
      else if ((c & 0xF8) == 0xF0)
        bytes = 4;
      else {
        isValidUtf8 = false;
        break;
      }
      if (i + bytes > rawName.size()) {
        isValidUtf8 = false;
        break;
      }
      for (int j = 1; j < bytes; ++j) {
        if ((rawName[i + j] & 0xC0) != 0x80) {
          isValidUtf8 = false;
          break;
        }
      }
      i += bytes - 1;
    } else if (!std::isprint(c) && !std::isspace(c) && c != '/') {
      isValidUtf8 = false;
      break;
    }
  }

  if (!isValidUtf8) {
    spdlog::warn("Invalid UTF-8 at offset {}, trying Shift-JIS", nameOffset);
    rawName = ConvertShiftJISToUTF8(rawName);
    std::vector<uint8_t> rawNameVec(rawName.begin(), rawName.end());
    if (IsBinaryData(rawNameVec)) {
      spdlog::warn("Name at offset {} appears binary, skipping", nameOffset);
      // Log hex dump for debugging
      std::ostringstream hex;
      hex << std::hex << std::setfill('0');
      for (size_t i = 0; i < std::min(length, size_t(32)); ++i) {
        hex << std::setw(2) << static_cast<int>(nameTable[nameOffset + i])
            << " ";
      }
      spdlog::debug("Name table data: {}", hex.str());
      return "";
    }
  }

  rawName.erase(std::find_if(rawName.rbegin(), rawName.rend(),
                             [](unsigned char c) {
                               return !std::isspace(c) && c != '\0';
                             })
                    .base(),
                rawName.end());

  rawName.erase(0, rawName.find_first_not_of('/'));
  rawName.erase(rawName.find_last_not_of('/') + 1);

  if (rawName.empty()) {
    spdlog::warn("Name at offset {} is empty after sanitization", nameOffset);
    return "";
  }

  spdlog::debug("Resolved item name: {}", rawName);
  return rawName;
}

std::string PKGInstaller::ExtractSafeFilename(
    const PKGItem &item, const std::vector<uint8_t> &nameTable, int itemIndex) {
  // Check for system file IDs first
  static const std::unordered_map<uint8_t, std::string> kSystemFileIDs = {
      {0x01, "eboot.bin"},         {0x02, "param.sfo"},
      {0x03, "icon0.png"},         {0x04, "pic1.png"},
      {0x05, "snd0.at9"},          {0x06, "changeinfo.xml"},
      {0x07, "trophy.trp"},        {0x08, "shareparam.json"},
      {0x09, "shareovr.png"},      {0x0A, "save_data.bin"},
      {0x0B, "right.sprx"},        {0x0C, "playgo.xml"},
      {0x0D, "app.pkg"},           {0x0E, "pronunciation.xml"},
      {0x0F, "pronunciation.snd"}, {0x10, "pic2.png"},
      {0x11, "pubtoolinfo.dat"},   {0x12, "app_ext.dat"},
      {0x13, "icon0_kana.png"},    {0x14, "icon0_icn.png"}};

  if (item.nameOffset == 0 &&
      kSystemFileIDs.find(item.type) != kSystemFileIDs.end()) {
    spdlog::info("Using system file ID 0x{:02X}: {}", item.type,
                 kSystemFileIDs.at(item.type));
    return SanitizeFileName(kSystemFileIDs.at(item.type));
  }

  // Try to extract name from name table
  if (item.nameOffset < nameTable.size() && item.nameSize > 0) {
    size_t end_pos = std::min(
        static_cast<size_t>(item.nameOffset + item.nameSize), nameTable.size());
    std::string raw_name(nameTable.begin() + item.nameOffset,
                         nameTable.begin() + end_pos);

    // Remove null terminator
    size_t null_pos = raw_name.find('\0');
    if (null_pos != std::string::npos) {
      raw_name = raw_name.substr(0, null_pos);
    }

    // Check if it's a valid filename
    std::vector<uint8_t> rawNameVec(raw_name.begin(), raw_name.end());
    if (!raw_name.empty() && !IsBinaryData(rawNameVec) &&
        raw_name.length() < 256) {
      return SanitizeFileName(raw_name);
    }
  }

  // Generate fallback name based on type
  std::string extension;
  switch (item.type) {
  case 0x01:
    extension = ".bin";
    break;
  case 0x02:
    extension = ".elf";
    break;
  case 0x03:
    extension = ".prx";
    break;
  case 0x04:
    extension = ".sfo";
    break;
  case 0x05:
    extension = ".png";
    break;
  case 0x06:
    extension = ".xml";
    break;
  case 0x07:
    extension = ".trp";
    break;
  case 0x08:
    extension = ".json";
    break;
  case 0x09:
    extension = ".at9";
    break;
  default:
    extension = ".dat";
    break;
  }

  return SanitizeFileName("item_" + std::to_string(itemIndex) + "_type_" +
                          std::to_string(item.type) + extension);
}

std::string PKGInstaller::SanitizeFileName(const std::string &filename) {
  if (filename.empty()) {
    return "unnamed_file";
  }

  std::string sanitized;
  sanitized.reserve(filename.length());

  // Replace invalid characters
  for (unsigned char c : filename) {
    if (c >= 32 && c <= 126) {
      // Replace Windows forbidden characters
      if (c == '<' || c == '>' || c == ':' || c == '"' || c == '/' ||
          c == '\\' || c == '|' || c == '?' || c == '*') {
        sanitized += '_';
      } else {
        sanitized += c;
      }
    } else if (sanitized.length() < 200) {
      // Convert non-printable characters to hex
      char hex[4];
      snprintf(hex, sizeof(hex), "%02X", c);
      sanitized += std::string("_") + hex;
    }

    // Limit filename length
    if (sanitized.length() > 240) {
      break;
    }
  }

  // Remove leading/trailing dots and spaces
  while (!sanitized.empty() &&
         (sanitized.front() == '.' || sanitized.front() == ' ')) {
    sanitized.erase(0, 1);
  }
  while (!sanitized.empty() &&
         (sanitized.back() == '.' || sanitized.back() == ' ')) {
    sanitized.pop_back();
  }

  // Check for Windows reserved names
  const std::vector<std::string> reserved_names = {
      "CON",  "PRN",  "AUX",  "NUL",  "COM1", "COM2", "COM3", "COM4",
      "COM5", "COM6", "COM7", "COM8", "COM9", "LPT1", "LPT2", "LPT3",
      "LPT4", "LPT5", "LPT6", "LPT7", "LPT8", "LPT9"};

  std::string upper_sanitized = sanitized;
  std::transform(upper_sanitized.begin(), upper_sanitized.end(),
                 upper_sanitized.begin(),
                 [](unsigned char c) { return std::toupper(c); });

  for (const auto &reserved : reserved_names) {
    if (upper_sanitized == reserved ||
        (upper_sanitized.length() > reserved.length() &&
         upper_sanitized.substr(0, reserved.length()) == reserved &&
         upper_sanitized[reserved.length()] == '.')) {
      sanitized = "_" + sanitized;
      break;
    }
  }

  // Final fallback if name is still invalid
  if (sanitized.empty() || sanitized.length() > 255) {
    std::hash<std::string> hasher;
    size_t hash = hasher(filename);
    sanitized = "file_" + std::to_string(hash);
  }

  return sanitized;
}

// Add helper method to check if data is binary
bool PKGInstaller::IsBinaryData(const std::vector<uint8_t> &data) const {
  if (data.empty()) {
    return false;
  }

  // Check for common binary file signatures
  if (data.size() >= 4) {
    // ELF magic
    if (data[0] == 0x7F && data[1] == 'E' && data[2] == 'L' && data[3] == 'F') {
      return true;
    }
    // PE magic
    if (data[0] == 'M' && data[1] == 'Z') {
      return true;
    }
  }

  // Check for high percentage of non-printable characters
  size_t nonPrintable = 0;
  size_t sampleSize = std::min(data.size(), size_t(1024));

  for (size_t i = 0; i < sampleSize; ++i) {
    if (data[i] < 32 || data[i] > 126) {
      nonPrintable++;
    }
  }

  // If more than 30% non-printable, consider it binary
  return (nonPrintable * 100 / sampleSize) > 30;
}

bool PKGInstaller::ParseSFO(
    const std::vector<uint8_t> &sfoData,
    std::unordered_map<std::string, std::string> &params) {
  try {
    if (sfoData.size() < sizeof(SFOHeader)) {
      spdlog::error("SFO data too small for header: {} bytes", sfoData.size());
      return false;
    }

    spdlog::debug("Parsing SFO data of size: {}", sfoData.size());

    const SFOHeader *header =
        reinterpret_cast<const SFOHeader *>(sfoData.data());

    // Convert from little-endian if needed
    uint32_t magic = header->magic;
    uint32_t paramCount = header->paramCount;
    uint32_t keyTableOffset = header->keyTableOffset;
    uint32_t dataTableOffset = header->dataTableOffset;

    // Debug: Log the first 16 bytes of SFO data
    spdlog::debug("SFO data first 16 bytes: {:02X} {:02X} {:02X} {:02X} {:02X} {:02X} {:02X} {:02X} {:02X} {:02X} {:02X} {:02X} {:02X} {:02X} {:02X} {:02X}",
                  sfoData[0], sfoData[1], sfoData[2], sfoData[3], sfoData[4], sfoData[5], sfoData[6], sfoData[7],
                  sfoData[8], sfoData[9], sfoData[10], sfoData[11], sfoData[12], sfoData[13], sfoData[14], sfoData[15]);

    spdlog::debug("SFO magic found: 0x{:08X}, expected: 0x{:08X}", magic, SFO_MAGIC);

    // Try multiple possible SFO magic values
    if (magic != SFO_MAGIC && magic != 0x00505346 && magic != 0x46535000) {
      spdlog::error("Invalid SFO magic: 0x{:08X} (expected 0x{:08X} or 0x{:08X})", magic, SFO_MAGIC, 0x00505346);
      return false;
    }

    if (keyTableOffset >= sfoData.size() || dataTableOffset >= sfoData.size()) {
      spdlog::error("Invalid SFO table offsets");
      return false;
    }

    for (uint32_t i = 0; i < paramCount; ++i) {
      size_t paramOffset = sizeof(SFOHeader) + i * sizeof(SFOParam);
      if (paramOffset + sizeof(SFOParam) > sfoData.size()) {
        break;
      }

      const SFOParam *param =
          reinterpret_cast<const SFOParam *>(sfoData.data() + paramOffset);

      // Extract key name
      size_t keyOffset = keyTableOffset + param->keyOffset;
      if (keyOffset >= sfoData.size()) {
        continue;
      }

      const char *keyPtr =
          reinterpret_cast<const char *>(sfoData.data() + keyOffset);
      std::string key(keyPtr, strnlen(keyPtr, sfoData.size() - keyOffset));

      // Extract value data
      size_t valueOffset = dataTableOffset + param->dataOffset;
      if (valueOffset >= sfoData.size()) {
        continue;
      }

      std::string value;
      if (param->format == 0x0204) { // UTF-8 string
        const char *valuePtr =
            reinterpret_cast<const char *>(sfoData.data() + valueOffset);
        value = std::string(
            valuePtr,
            strnlen(valuePtr, std::min(param->length,
                                       static_cast<uint32_t>(sfoData.size() -
                                                             valueOffset))));
      } else if (param->format == 0x0404) { // 32-bit integer
        if (valueOffset + 4 <= sfoData.size()) {
          uint32_t intValue =
              *reinterpret_cast<const uint32_t *>(sfoData.data() + valueOffset);
          value = std::to_string(intValue);
        }
      }

      if (!key.empty()) {
        params[key] = value;
      }
    }

    return true;
  } catch (const std::exception &e) {
    spdlog::error("ParseSFO failed: {}", e.what());
    return false;
  }
}

bool PKGInstaller::MergeUpdateFiles(
    std::unordered_map<std::string, std::vector<uint8_t>> &baseFiles,
    const std::unordered_map<std::string, std::vector<uint8_t>> &updateFiles) {
  try {
    for (const auto &[filename, data] : updateFiles) {
      baseFiles[filename] = data; // Replace or add the file
      spdlog::debug("Merged update file: {}", filename);
    }
    return true;
  } catch (const std::exception &e) {
    spdlog::error("MergeUpdateFiles failed: {}", e.what());
    return false;
  }
}

std::string PKGInstaller::ComputeSHA256(const std::vector<uint8_t> &data) {
  try {
    unsigned char hash[SHA256_DIGEST_LENGTH];
    SHA256_CTX sha256;
    SHA256_Init(&sha256);
    SHA256_Update(&sha256, data.data(), data.size());
    SHA256_Final(hash, &sha256);

    std::ostringstream ss;
    for (int i = 0; i < SHA256_DIGEST_LENGTH; ++i) {
      ss << std::hex << std::setw(2) << std::setfill('0') << (int)hash[i];
    }
    return ss.str();
  } catch (const std::exception &e) {
    spdlog::error("ComputeSHA256 failed: {}", e.what());
    return "";
  }
}

bool PKGInstaller::VerifyPackageIntegrity(
    const PKGHeader &header, const std::vector<uint8_t> &packageData) {
  try {
    // Basic integrity checks
    if (packageData.size() < header.content_offset + header.content_size) {
      spdlog::error("Package data smaller than expected content size");
      return false;
    }

    // Additional integrity verification could be added here
    // For now, we'll just verify the basic structure
    return true;
  } catch (const std::exception &e) {
    spdlog::error("VerifyPackageIntegrity failed: {}", e.what());
    return false;
  }
}

std::string PKGInstaller::CreateInstallDirectory(const std::string &installPath,
                                                 const std::string &contentId) {
  try {
    // build via std::filesystem to avoid manual “/”
    std::filesystem::path p = installPath;
    if (!contentId.empty())
      p /= contentId;
    std::string full = p.generic_string();

    // strip any double‐mount: e.g. "/app0/app0/XYZ" → "/app0/XYZ"
    std::string dup = m_installRoot + m_installRoot;
    if (full.rfind(dup, 0) == 0) {
      full = m_installRoot + full.substr(dup.size());
    }

    m_filesystem->CreateVirtualDirectory(full);
    return full;
  } catch (const std::exception &e) {
    spdlog::error("CreateInstallDirectory failed: {}", e.what());
    return installPath;
  }
}

bool PKGInstaller::SaveInstalledPackages() const {
  try {
    std::string configPath = "./ps4_root/installed_packages.json";
    std::filesystem::create_directories(
        std::filesystem::path(configPath).parent_path());

    std::ofstream file(configPath);
    if (!file.is_open()) {
      spdlog::error("Failed to open installed packages file for writing: {}",
                    configPath);
      return false;
    }

    file << "{\n";
    file << "  \"installed_packages\": {\n";

    bool first = true;
    for (const auto &[contentId, installPath] : m_installedPackages) {
      if (!first) {
        file << ",\n";
      }
      file << "    \"" << contentId << "\": \"" << installPath << "\"";
      first = false;
    }

    file << "\n  }\n";
    file << "}\n";

    file.close();
    spdlog::info("Saved {} installed packages to {}",
                 m_installedPackages.size(), configPath);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("Failed to save installed packages: {}", e.what());
    return false;
  }
}

bool PKGInstaller::LoadInstalledPackages() {
  try {
    std::string configPath = "./ps4_root/installed_packages.json";

    if (!std::filesystem::exists(configPath)) {
      spdlog::info(
          "No installed packages file found at {}, starting with empty list",
          configPath);
      return true;
    }

    std::ifstream file(configPath);
    if (!file.is_open()) {
      spdlog::error("Failed to open installed packages file for reading: {}",
                    configPath);
      return false;
    }

    std::string line;
    std::string content;
    while (std::getline(file, line)) {
      content += line + "\n";
    }
    file.close();

    // Simple JSON parsing for the specific format we use
    size_t packagesStart = content.find("\"installed_packages\":");
    if (packagesStart == std::string::npos) {
      spdlog::warn("Invalid installed packages file format");
      return false;
    }

    size_t braceStart = content.find("{", packagesStart);
    size_t braceEnd = content.find("}", braceStart);
    if (braceStart == std::string::npos || braceEnd == std::string::npos) {
      spdlog::warn("Invalid installed packages file format");
      return false;
    }

    std::string packagesSection =
        content.substr(braceStart + 1, braceEnd - braceStart - 1);

    // Parse each package entry
    std::lock_guard<std::mutex> lock(const_cast<std::mutex &>(m_installMutex));
    m_installedPackages.clear();

    std::istringstream stream(packagesSection);
    std::string packageLine;
    while (std::getline(stream, packageLine)) {
      // Look for pattern: "CONTENT_ID": "INSTALL_PATH"
      size_t colonPos = packageLine.find(":");
      if (colonPos == std::string::npos)
        continue;

      std::string contentIdPart = packageLine.substr(0, colonPos);
      std::string installPathPart = packageLine.substr(colonPos + 1);

      // Extract content ID (remove quotes and whitespace)
      size_t contentIdStart = contentIdPart.find("\"");
      size_t contentIdEnd = contentIdPart.find("\"", contentIdStart + 1);
      if (contentIdStart == std::string::npos ||
          contentIdEnd == std::string::npos)
        continue;

      std::string contentId = contentIdPart.substr(
          contentIdStart + 1, contentIdEnd - contentIdStart - 1);

      // Extract install path (remove quotes and whitespace)
      size_t pathStart = installPathPart.find("\"");
      size_t pathEnd = installPathPart.find("\"", pathStart + 1);
      if (pathStart == std::string::npos || pathEnd == installPathPart.npos)
        continue;

      std::string installPath =
          installPathPart.substr(pathStart + 1, pathEnd - pathStart - 1);

      m_installedPackages[contentId] = installPath;
      spdlog::debug("Loaded installed package: {} -> {}", contentId,
                    installPath);
    }

    spdlog::info("Loaded {} installed packages from {}",
                 m_installedPackages.size(), configPath);
    // sanitize any duplicate mount on load
    for (auto &kv : m_installedPackages) {
      std::string &path = kv.second;
      std::string dup = m_installRoot + m_installRoot;
      if (path.rfind(dup, 0) == 0) {
        path = m_installRoot + path.substr(dup.size());
      }
    }
    return true;
  } catch (const std::exception &e) {
    spdlog::error("Failed to load installed packages: {}", e.what());
    return false;
  }
}

const std::string &
PKGInstaller::GetInstallPath(const std::string &contentId) const noexcept {
  auto it = m_installedPackages.find(contentId);
  if (it != m_installedPackages.end()) {
    return it->second;
  }
  static const std::string empty;
  return empty;
}

} // namespace ps4