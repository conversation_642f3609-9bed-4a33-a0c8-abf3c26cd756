// Copyright 2025 <Copyright Owner>

#define SDL_MAIN_HANDLED
#define SPDLOG_NO_EXCEPTIONS
#include "cpu/cpu_diagnostics.h"
#include "gui/backends/imgui_impl_sdl2.h"
#include "gui/backends/imgui_impl_vulkan.h"
#include "gui/imgui.h"
#include "gui/imgui_freetype.h"
#include "gui/input_manager.h"
#include "jit/jit_diagnostics.h"
#include "loader/pkg_installer.h"
#include "memory/memory_diagnostics.h"
#include "ps4/ps4_emulator.h"
#include "ps4/ps4_filesystem.h"
#include "ps4/ps4_gpu.h"
#include "video_core/shader_emulator.h"
#include "video_core/tile_manager.h"
#include <SDL2/SDL.h>
#include <algorithm>
#include <chrono>
#include <cstdlib>
#include <cstring>
#include <filesystem>
#include <fstream>
#include <future>
#include <iostream>
#include <memory>
#include <shared_mutex>
#include <spdlog/fmt/fmt.h>
#include <spdlog/spdlog.h>
#include <string>
#include <thread>
#include <vector>

#ifdef _WIN32
#define NOMINMAX
#include <commdlg.h>
#include <shellscalingapi.h>
#include <shlobj.h>
#include <windows.h>
#pragma comment(lib, "Shcore.lib")   // for SetProcessDpiAwareness
#pragma comment(lib, "comdlg32.lib") // for file dialogs
#pragma comment(lib, "shell32.lib")  // for folder browser

static void EnablePerMonitorV2DpiAwareness() {
  // Windows 10 Creators Update and newer
  if (auto fn = reinterpret_cast<decltype(&SetProcessDpiAwarenessContext)>(
          GetProcAddress(GetModuleHandleA("user32.dll"),
                         "SetProcessDpiAwarenessContext"))) {
    fn(DPI_AWARENESS_CONTEXT_PER_MONITOR_AWARE_V2);
    return;
  }

  // Older – fall back to per-monitor DPI aware
  SetProcessDpiAwareness(PROCESS_PER_MONITOR_DPI_AWARE);
}
#endif

#ifdef CreateDirectory
#undef CreateDirectory
#endif

#ifdef _WIN32
// sys/cdefs.h is not available on Windows.
#else
#include <sys/cdefs.h>
#endif
#include <SDL2/SDL_vulkan.h>

namespace ps4 {

/**
 * @brief Comprehensive settings for the emulator with serialization and thread
 * safety.
 */
struct EmulatorSettings {
  // Performance Metrics (Grouped for potential cache benefits)
  uint64_t cacheHits = 0;
  uint64_t cacheMisses = 0;

  // Paths and Directories (std::string can be large)
  std::vector<std::string> recent_games;  ///< Recently played games
  std::string last_game_path;             ///< Last loaded game path
  std::string audio_device = "default";   ///< Audio output device
  std::string gpu_backend = "Vulkan";     ///< GPU backend (Vulkan/OpenGL)
  std::string aa_method = "FXAA";         ///< AA method
  std::string game_directory = "";        ///< Default game directory
  std::string save_directory = "";        ///< Save game directory
  std::string state_directory = "";       ///< Save state directory
  std::string screenshot_directory = "";  ///< Screenshot directory
  std::string dump_directory = "";        ///< Memory dump directory
  std::string log_level = "info";         ///< Logging level
  std::string network_interface = "auto"; ///< Network interface

  // Integers (Various sizes, grouped by typical usage)
  int width = 1280;             ///< Window width
  int height = 720;             ///< Window height
  int framerate_limit = 60;     ///< FPS limit
  int auto_save_interval = 300; ///< Auto-save interval in seconds
  int audio_latency = 50;       ///< Audio latency in ms
  int cpu_thread_count = 8;     ///< Number of CPU threads
  int cache_size_mb = 256;      ///< CPU cache size in MB
  int resolution_scale = 1;     ///< Resolution scaling factor
  int anisotropy_level = 16;    ///< Anisotropy level
  int memory_size_gb = 8;       ///< System memory size in GB
  int swap_size_gb = 4;         ///< Swap file size in GB
  int network_port = 9302;      ///< Network port

  // Floats
  float ui_scale = 1.0f;            ///< UI scaling factor
  float emulation_speed = 1.0f;     ///< Emulation speed multiplier
  float master_volume = 1.0f;       ///< Master audio volume
  float sfx_volume = 1.0f;          ///< Sound effects volume
  float music_volume = 1.0f;        ///< Music volume
  float controller_deadzone = 0.1f; ///< Controller deadzone

  // Booleans (Smallest, grouped together)
  bool fullscreen = false;           ///< Fullscreen mode
  bool vsync = true;                 ///< VSync enabled
  bool pause_on_focus_loss = true;   ///< Pause when window loses focus
  bool auto_save_states = true;      ///< Auto-save states periodically
  bool audio_enabled = true;         ///< Audio enabled flag
  bool jit_enabled = true;           ///< JIT compilation enabled
  bool simd_optimizations = true;    ///< SIMD optimizations
  bool branch_prediction = true;     ///< Branch prediction
  bool anisotropic_filtering = true; ///< Anisotropic filtering
  bool anti_aliasing = false;        ///< Anti-aliasing enabled
  bool shader_cache = true;          ///< Shader cache enabled
  bool async_shaders = true;         ///< Async shader compilation
  bool memory_compression = true;    ///< Memory compression
  bool auto_mount_games = true;      ///< Auto-mount game files
  bool debug_mode = false;           ///< Debug mode enabled
  bool show_fps = true;              ///< Show FPS counter
  bool show_performance = false;     ///< Show performance metrics
  bool log_syscalls = false;         ///< Log system calls
  bool log_gpu_commands = false;     ///< Log GPU commands
  bool controller_enabled = true;    ///< Controller support
  bool keyboard_enabled = true;      ///< Keyboard support
  bool mouse_enabled = true;         ///< Mouse support
  bool network_enabled = false;      ///< Network support
  bool strict_mode = false;          ///< Strict compatibility mode
  bool ignore_missing_imports =
      false;                ///< Ignore missing imports for compatibility
  bool patch_games = false; ///< Apply game-specific patches

  void Serialize(std::ostream &out) const {
    out << "version=1\n";

    // Window and Display Settings
    out << "last_game_path=" << last_game_path << "\n";
    out << "width=" << width << "\n";
    out << "height=" << height << "\n";
    out << "fullscreen=" << (fullscreen ? "1" : "0") << "\n";
    out << "vsync=" << (vsync ? "1" : "0") << "\n";
    out << "framerate_limit=" << framerate_limit << "\n";
    out << "ui_scale=" << ui_scale << "\n";

    // Emulation Settings
    out << "emulation_speed=" << emulation_speed << "\n";
    out << "pause_on_focus_loss=" << (pause_on_focus_loss ? "1" : "0") << "\n";
    out << "auto_save_states=" << (auto_save_states ? "1" : "0") << "\n";
    out << "auto_save_interval=" << auto_save_interval << "\n";

    // Audio Settings
    out << "audio_enabled=" << (audio_enabled ? "1" : "0") << "\n";
    out << "master_volume=" << master_volume << "\n";
    out << "sfx_volume=" << sfx_volume << "\n";
    out << "music_volume=" << music_volume << "\n";
    out << "audio_latency=" << audio_latency << "\n";
    out << "audio_device=" << audio_device << "\n";

    // CPU Settings
    out << "cpu_thread_count=" << cpu_thread_count << "\n";
    out << "jit_enabled=" << (jit_enabled ? "1" : "0") << "\n";
    out << "simd_optimizations=" << (simd_optimizations ? "1" : "0") << "\n";
    out << "branch_prediction=" << (branch_prediction ? "1" : "0") << "\n";
    out << "cache_size_mb=" << cache_size_mb << "\n";

    // GPU Settings
    out << "gpu_backend=" << gpu_backend << "\n";
    out << "resolution_scale=" << resolution_scale << "\n";
    out << "anisotropic_filtering=" << (anisotropic_filtering ? "1" : "0")
        << "\n";
    out << "anisotropy_level=" << anisotropy_level << "\n";
    out << "anti_aliasing=" << (anti_aliasing ? "1" : "0") << "\n";
    out << "aa_method=" << aa_method << "\n";
    out << "shader_cache=" << (shader_cache ? "1" : "0") << "\n";
    out << "async_shaders=" << (async_shaders ? "1" : "0") << "\n";

    // Memory Settings
    out << "memory_size_gb=" << memory_size_gb << "\n";
    out << "memory_compression=" << (memory_compression ? "1" : "0") << "\n";
    out << "swap_size_gb=" << swap_size_gb << "\n";

    // File System Settings
    out << "game_directory=" << game_directory << "\n";
    out << "save_directory=" << save_directory << "\n";
    out << "state_directory=" << state_directory << "\n";
    out << "screenshot_directory=" << screenshot_directory << "\n";
    out << "dump_directory=" << dump_directory << "\n";
    out << "auto_mount_games=" << (auto_mount_games ? "1" : "0") << "\n";

    // Debug Settings
    out << "debug_mode=" << (debug_mode ? "1" : "0") << "\n";
    out << "show_fps=" << (show_fps ? "1" : "0") << "\n";
    out << "show_performance=" << (show_performance ? "1" : "0") << "\n";
    out << "log_syscalls=" << (log_syscalls ? "1" : "0") << "\n";
    out << "log_gpu_commands=" << (log_gpu_commands ? "1" : "0") << "\n";
    out << "log_level=" << log_level << "\n";

    // Input Settings
    out << "controller_enabled=" << (controller_enabled ? "1" : "0") << "\n";
    out << "controller_deadzone=" << controller_deadzone << "\n";
    out << "keyboard_enabled=" << (keyboard_enabled ? "1" : "0") << "\n";
    out << "mouse_enabled=" << (mouse_enabled ? "1" : "0") << "\n";

    // Network Settings
    out << "network_enabled=" << (network_enabled ? "1" : "0") << "\n";
    out << "network_interface=" << network_interface << "\n";
    out << "network_port=" << network_port << "\n";

    // Compatibility Settings
    out << "strict_mode=" << (strict_mode ? "1" : "0") << "\n";
    out << "ignore_missing_imports=" << (ignore_missing_imports ? "1" : "0")
        << "\n";
    out << "patch_games=" << (patch_games ? "1" : "0") << "\n";

    // Recent games and stats
    out << "recent_game_count=" << recent_games.size() << "\n";
    for (size_t i = 0; i < recent_games.size(); ++i) {
      out << "recent_game" << i << "=" << recent_games[i] << "\n";
    }
    out << "cacheHits=" << cacheHits << "\n";
    out << "cacheMisses=" << cacheMisses << "\n";
  }

  void Deserialize(std::istream &in) {
    std::string line;
    size_t recent_game_count = 0;
    uint32_t loaded_version_from_file =
        0; // Used to check if version was in file

    while (std::getline(in, line)) {
      auto pos = line.find('=');
      if (pos == std::string::npos)
        continue;
      auto key = line.substr(0, pos);
      auto value = line.substr(pos + 1);
      try {
        if (key == "version")
          loaded_version_from_file = std::stoul(value);
        // Window and Display Settings
        else if (key == "last_game_path")
          last_game_path = value;
        else if (key == "width")
          width = std::stoi(value);
        else if (key == "height")
          height = std::stoi(value);
        else if (key == "fullscreen")
          fullscreen = (value == "1");
        else if (key == "vsync")
          vsync = (value == "1");
        else if (key == "framerate_limit")
          framerate_limit = std::stoi(value);
        else if (key == "ui_scale")
          ui_scale = std::stof(value);
        // Emulation Settings
        else if (key == "emulation_speed")
          emulation_speed = std::stof(value);
        else if (key == "pause_on_focus_loss")
          pause_on_focus_loss = (value == "1");
        else if (key == "auto_save_states")
          auto_save_states = (value == "1");
        else if (key == "auto_save_interval")
          auto_save_interval = std::stoi(value);
        // Audio Settings
        else if (key == "audio_enabled")
          audio_enabled = (value == "1");
        else if (key == "master_volume")
          master_volume = std::stof(value);
        else if (key == "sfx_volume")
          sfx_volume = std::stof(value);
        else if (key == "music_volume")
          music_volume = std::stof(value);
        else if (key == "audio_latency")
          audio_latency = std::stoi(value);
        else if (key == "audio_device")
          audio_device = value;
        // CPU Settings
        else if (key == "cpu_thread_count")
          cpu_thread_count = std::stoi(value);
        else if (key == "jit_enabled")
          jit_enabled = (value == "1");
        else if (key == "simd_optimizations")
          simd_optimizations = (value == "1");
        else if (key == "branch_prediction")
          branch_prediction = (value == "1");
        else if (key == "cache_size_mb")
          cache_size_mb = std::stoi(value);
        // GPU Settings
        else if (key == "gpu_backend")
          gpu_backend = value;
        else if (key == "resolution_scale")
          resolution_scale = std::stoi(value);
        else if (key == "anisotropic_filtering")
          anisotropic_filtering = (value == "1");
        else if (key == "anisotropy_level")
          anisotropy_level = std::stoi(value);
        else if (key == "anti_aliasing")
          anti_aliasing = (value == "1");
        else if (key == "aa_method")
          aa_method = value;
        else if (key == "shader_cache")
          shader_cache = (value == "1");
        else if (key == "async_shaders")
          async_shaders = (value == "1");
        // Memory Settings
        else if (key == "memory_size_gb")
          memory_size_gb = std::stoi(value);
        else if (key == "memory_compression")
          memory_compression = (value == "1");
        else if (key == "swap_size_gb")
          swap_size_gb = std::stoi(value);
        // File System Settings
        else if (key == "game_directory")
          game_directory = value;
        else if (key == "save_directory")
          save_directory = value;
        else if (key == "state_directory")
          state_directory = value;
        else if (key == "screenshot_directory")
          screenshot_directory = value;
        else if (key == "dump_directory")
          dump_directory = value;
        else if (key == "auto_mount_games")
          auto_mount_games = (value == "1");
        // Debug Settings
        else if (key == "debug_mode")
          debug_mode = (value == "1");
        else if (key == "show_fps")
          show_fps = (value == "1");
        else if (key == "show_performance")
          show_performance = (value == "1");
        else if (key == "log_syscalls")
          log_syscalls = (value == "1");
        else if (key == "log_gpu_commands")
          log_gpu_commands = (value == "1");
        else if (key == "log_level")
          log_level = value;
        // Input Settings
        else if (key == "controller_enabled")
          controller_enabled = (value == "1");
        else if (key == "controller_deadzone")
          controller_deadzone = std::stof(value);
        else if (key == "keyboard_enabled")
          keyboard_enabled = (value == "1");
        else if (key == "mouse_enabled")
          mouse_enabled = (value == "1");
        // Network Settings
        else if (key == "network_enabled")
          network_enabled = (value == "1");
        else if (key == "network_interface")
          network_interface = value;
        else if (key == "network_port")
          network_port = std::stoi(value);
        // Compatibility Settings
        else if (key == "strict_mode")
          strict_mode = (value == "1");
        else if (key == "ignore_missing_imports")
          ignore_missing_imports = (value == "1");
        else if (key == "patch_games")
          patch_games = (value == "1");
        // Recent games and stats
        else if (key == "recent_game_count")
          recent_game_count = std::stoul(value);
        else if (key.rfind("recent_game", 0) == 0)
          recent_games.push_back(value);
        else if (key == "cacheHits")
          cacheHits = std::stoull(value);
        else if (key == "cacheMisses")
          cacheMisses = std::stoull(value);
      } catch (const std::exception &e) {
        spdlog::error("Settings::Deserialize failed for key {}: {}", key,
                      e.what());
        throw std::runtime_error("Invalid settings format");
      }
    }
    // If no version was found in the file, assume it's a new settings file
    if (loaded_version_from_file == 0) {
      spdlog::info("No settings version found, creating new settings file with "
                   "version 1");
      // version = 1; // No need to assign to a local 'version' if it's not used
      // further
    } else if (loaded_version_from_file != 1) {
      spdlog::error("Unsupported settings version: {}",
                    loaded_version_from_file);
      throw std::runtime_error("Unsupported settings version");
    }
    if (recent_games.size() > recent_game_count) {
      recent_games.resize(recent_game_count);
    }
  }
};

static constexpr int kDefaultWidth = 1280;
static constexpr int kDefaultHeight = 720;
static constexpr const char *kWindowTitle =
    "PS4 Emulator - Advanced Configuration";
static EmulatorSettings settings;
static char gamePathBuf[260] = "";
static char statePathBuf[260] = "";

// Main window flags
static bool show_load_game_window = false;
static bool show_save_state_window = false;
static bool show_load_state_window = false;
static bool show_filesystem_window = false;
static bool show_diagnostics_window = false;
static bool show_input_window = false;
static bool show_font_debug_window = false;

// PKG installation window flags
static bool show_install_pkg_window = false;
static bool show_set_game_directory_window = false;

// Settings window flags
static bool show_display_settings = false;
static bool show_audio_settings = false;
static bool show_cpu_settings = false;
static bool show_gpu_settings = false;
static bool show_memory_settings = false;
static bool show_filesystem_settings = false;
static bool show_debug_settings = false;
static bool show_input_settings = false;
static bool show_network_settings = false;
static bool show_compatibility_settings = false;
static bool show_advanced_settings = false;

// Game browser
static bool show_game_browser = false;
static std::vector<std::string> game_list;
static std::string selected_game = "";

// Performance overlay
static bool show_performance_overlay = false;

static std::shared_mutex settingsMutex;

/**
 * @brief Gets the settings file path.
 */
static std::filesystem::path getSettingsPath() {
  auto env = std::getenv("APPDATA");
  std::filesystem::path dir = env ? env : std::filesystem::current_path();
  dir /= "PS4Emulator";
  try {
    std::filesystem::create_directories(dir);
  } catch (const std::exception &e) {
    spdlog::error("Failed to create settings directory {}: {}", dir.string(),
                  e.what());
  }
  return dir / "settings.ini";
}

/**
 * @brief Loads emulator settings from file.
 */
static void LoadSettings() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(settingsMutex);
  try {
    auto path = getSettingsPath();
    std::ifstream in(path);
    if (in) {
      settings.Deserialize(in);
      settings.cacheHits++;
      auto end = std::chrono::high_resolution_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      spdlog::info("LoadSettings: Loaded from {}, latency={}us", path.string(),
                   latency);
    } else {
      settings.cacheMisses++;
      spdlog::warn("LoadSettings: No settings file found at {}", path.string());
    }
  } catch (const std::exception &e) {
    settings.cacheMisses++;
    spdlog::error("LoadSettings failed: {}", e.what());
  }
}

/**
 * @brief Saves emulator settings to file.
 */
static void SaveSettings() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(settingsMutex);
  try {
    auto path = getSettingsPath();
    auto tmp = path;
    tmp += ".tmp";
    std::ofstream out(tmp);
    settings.Serialize(out);
    out.close();
    std::filesystem::rename(tmp, path);
    settings.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("SaveSettings: Saved to {}, latency={}us", path.string(),
                 latency);
  } catch (const std::exception &e) {
    settings.cacheMisses++;
    spdlog::error("SaveSettings failed: {}", e.what());
  }
}

static void check_vk_result(VkResult err) {
  if (err == VK_SUCCESS)
    return;
  spdlog::error("[vulkan] Error: VkResult = {}", static_cast<int>(err));
  if (err < 0)
    throw std::runtime_error("Vulkan critical error");
}

/**
 * @brief Renders a frame with ImGui and game output.
 */
void renderFrame(VulkanContext &vk_context,
                 VkCommandBuffer render_command_buffer, ImDrawData *draw_data,
                 uint32_t &image_index, bool &swapchain_rebuild, bool &quit,
                 PS4Emulator &emulator) {
  auto start = std::chrono::high_resolution_clock::now();
  ImVec4 clear_color = ImVec4(0.12f, 0.12f, 0.12f, 1.00f);
  std::unique_lock<std::shared_mutex> lock(vk_context.contextMutex);
  try {
    vkWaitForFences(vk_context.device, 1, &vk_context.inFlightFence, VK_TRUE,
                    UINT64_MAX);
    VkResult result = vkAcquireNextImageKHR(
        vk_context.device, vk_context.swapchain, UINT64_MAX,
        vk_context.imageAvailableSemaphore, VK_NULL_HANDLE, &image_index);
    if (result == VK_ERROR_OUT_OF_DATE_KHR || result == VK_SUBOPTIMAL_KHR) {
      swapchain_rebuild = true;
      vk_context.cacheHits++;
      return;
    } else if (result != VK_SUCCESS) {
      vk_context.cacheMisses++;
      spdlog::error(fmt::format("Failed to acquire swapchain image: {}",
                                static_cast<int>(result)));
      quit = true;
      return;
    }
    vkResetFences(vk_context.device, 1, &vk_context.inFlightFence);
    vkResetCommandBuffer(render_command_buffer, 0);
    VkCommandBufferBeginInfo begin_info = {
        VK_STRUCTURE_TYPE_COMMAND_BUFFER_BEGIN_INFO};
    if (vkBeginCommandBuffer(render_command_buffer, &begin_info) !=
        VK_SUCCESS) {
      vk_context.cacheMisses++;
      spdlog::error(fmt::format("Failed to begin recording command buffer"));
      quit = true;
      return;
    }
    VkClearValue clear_value = {};
    clear_value.color.float32[0] = clear_color.x * clear_color.w;
    clear_value.color.float32[1] = clear_color.y * clear_color.w;
    clear_value.color.float32[2] = clear_color.z * clear_color.w;
    clear_value.color.float32[3] = clear_color.w;
    VkRenderPassBeginInfo render_pass_info = {
        VK_STRUCTURE_TYPE_RENDER_PASS_BEGIN_INFO,
        nullptr,
        vk_context.renderPass,
        vk_context.framebuffers[image_index],
        {{0, 0}, vk_context.swapchainExtent},
        1,
        &clear_value};
    vkCmdBeginRenderPass(render_command_buffer, &render_pass_info,
                         VK_SUBPASS_CONTENTS_INLINE);
    ImGui_ImplVulkan_RenderDrawData(draw_data, render_command_buffer);
    vkCmdEndRenderPass(render_command_buffer);
    if (vkEndCommandBuffer(render_command_buffer) != VK_SUCCESS) {
      vk_context.cacheMisses++;
      spdlog::error(fmt::format("Failed to record command buffer"));
      quit = true;
      return;
    }
    VkPipelineStageFlags wait_stages[] = {
        VK_PIPELINE_STAGE_COLOR_ATTACHMENT_OUTPUT_BIT};
    VkSubmitInfo submit_info = {VK_STRUCTURE_TYPE_SUBMIT_INFO,
                                nullptr,
                                1,
                                &vk_context.imageAvailableSemaphore,
                                wait_stages,
                                1,
                                &render_command_buffer,
                                1,
                                &vk_context.renderFinishedSemaphore};
    if (vkQueueSubmit(vk_context.graphicsQueue, 1, &submit_info,
                      vk_context.inFlightFence) != VK_SUCCESS) {
      vk_context.cacheMisses++;
      spdlog::error(fmt::format("Failed to submit draw command buffer"));
      quit = true;
      return;
    }
    VkPresentInfoKHR present_info = {VK_STRUCTURE_TYPE_PRESENT_INFO_KHR,
                                     nullptr,
                                     1,
                                     &vk_context.renderFinishedSemaphore,
                                     1,
                                     &vk_context.swapchain,
                                     &image_index,
                                     nullptr};
    result = vkQueuePresentKHR(vk_context.graphicsQueue, &present_info);
    if (result == VK_ERROR_OUT_OF_DATE_KHR || result == VK_SUBOPTIMAL_KHR) {
      swapchain_rebuild = true;
    } else if (result != VK_SUCCESS) {
      vk_context.cacheMisses++;
      spdlog::error(fmt::format("Failed to present swapchain image: {}",
                                static_cast<int>(result)));
      quit = true;
    }
    vk_context.cacheHits++;
    vk_context.frameCount++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    vk_context.renderLatencyUs += latency;
    spdlog::trace(fmt::format("renderFrame: frame={}, latency={}us",
                              vk_context.frameCount, latency));
  } catch (const std::exception &e) {
    vk_context.cacheMisses++;
    spdlog::error("renderFrame failed: {}", e.what());
    quit = true;
  }
}

/**
 * @brief Creates the Vulkan swapchain.
 */
void createSwapchain(VulkanContext &vk_context, SDL_Window *window) {
  VkSurfaceCapabilitiesKHR capabilities;
  vkGetPhysicalDeviceSurfaceCapabilitiesKHR(vk_context.physicalDevice,
                                            vk_context.surface, &capabilities);

  uint32_t formatCount;
  vkGetPhysicalDeviceSurfaceFormatsKHR(
      vk_context.physicalDevice, vk_context.surface, &formatCount, nullptr);
  std::vector<VkSurfaceFormatKHR> formats(formatCount);
  vkGetPhysicalDeviceSurfaceFormatsKHR(vk_context.physicalDevice,
                                       vk_context.surface, &formatCount,
                                       formats.data());

  // Prefer sRGB format for better color reproduction
  VkSurfaceFormatKHR surfaceFormat = formats[0]; // Default fallback
  for (const auto &format : formats) {
    if (format.format == VK_FORMAT_B8G8R8A8_SRGB &&
        format.colorSpace == VK_COLOR_SPACE_SRGB_NONLINEAR_KHR) {
      surfaceFormat = format;
      break;
    }
    // Also accept regular UNORM if sRGB not available
    if (format.format == VK_FORMAT_B8G8R8A8_UNORM &&
        format.colorSpace == VK_COLOR_SPACE_SRGB_NONLINEAR_KHR) {
      surfaceFormat = format;
    }
  }

  uint32_t presentModeCount;
  vkGetPhysicalDeviceSurfacePresentModesKHR(vk_context.physicalDevice,
                                            vk_context.surface,
                                            &presentModeCount, nullptr);
  std::vector<VkPresentModeKHR> presentModes(presentModeCount);
  vkGetPhysicalDeviceSurfacePresentModesKHR(
      vk_context.physicalDevice, vk_context.surface, &presentModeCount,
      presentModes.data());
  VkPresentModeKHR presentMode = VK_PRESENT_MODE_FIFO_KHR;

  // Use drawable size instead of window size for high-DPI displays
  int width, height;
  SDL_Vulkan_GetDrawableSize(window, &width, &height);
  VkExtent2D extent = {static_cast<uint32_t>(width),
                       static_cast<uint32_t>(height)};
  extent.width =
      std::max(capabilities.minImageExtent.width,
               std::min(capabilities.maxImageExtent.width, extent.width));
  extent.height =
      std::max(capabilities.minImageExtent.height,
               std::min(capabilities.maxImageExtent.height, extent.height));

  VkSwapchainCreateInfoKHR swapchainInfo = {};
  swapchainInfo.sType = VK_STRUCTURE_TYPE_SWAPCHAIN_CREATE_INFO_KHR;
  swapchainInfo.surface = vk_context.surface;
  swapchainInfo.minImageCount = capabilities.minImageCount + 1;
  if (capabilities.maxImageCount > 0 &&
      swapchainInfo.minImageCount > capabilities.maxImageCount) {
    swapchainInfo.minImageCount = capabilities.maxImageCount;
  }
  swapchainInfo.imageFormat = surfaceFormat.format;
  swapchainInfo.imageColorSpace = surfaceFormat.colorSpace;
  swapchainInfo.imageExtent = extent;
  swapchainInfo.imageArrayLayers = 1;
  swapchainInfo.imageUsage = VK_IMAGE_USAGE_COLOR_ATTACHMENT_BIT;
  swapchainInfo.preTransform = capabilities.currentTransform;
  swapchainInfo.compositeAlpha = VK_COMPOSITE_ALPHA_OPAQUE_BIT_KHR;
  swapchainInfo.presentMode = presentMode;
  swapchainInfo.clipped = VK_TRUE;
  swapchainInfo.oldSwapchain = VK_NULL_HANDLE;

  if (vkCreateSwapchainKHR(vk_context.device, &swapchainInfo, nullptr,
                           &vk_context.swapchain) != VK_SUCCESS) {
    throw std::runtime_error("Failed to create swapchain");
  }

  uint32_t imageCount;
  vkGetSwapchainImagesKHR(vk_context.device, vk_context.swapchain, &imageCount,
                          nullptr);
  vk_context.swapchainImages.resize(imageCount);
  vkGetSwapchainImagesKHR(vk_context.device, vk_context.swapchain, &imageCount,
                          vk_context.swapchainImages.data());

  vk_context.swapchainImageViews.resize(imageCount);
  for (size_t i = 0; i < imageCount; i++) {
    VkImageViewCreateInfo viewInfo = {};
    viewInfo.sType = VK_STRUCTURE_TYPE_IMAGE_VIEW_CREATE_INFO;
    viewInfo.image = vk_context.swapchainImages[i];
    viewInfo.viewType = VK_IMAGE_VIEW_TYPE_2D;
    viewInfo.format = surfaceFormat.format;
    viewInfo.components.r = VK_COMPONENT_SWIZZLE_IDENTITY;
    viewInfo.components.g = VK_COMPONENT_SWIZZLE_IDENTITY;
    viewInfo.components.b = VK_COMPONENT_SWIZZLE_IDENTITY;
    viewInfo.components.a = VK_COMPONENT_SWIZZLE_IDENTITY;
    viewInfo.subresourceRange.aspectMask = VK_IMAGE_ASPECT_COLOR_BIT;
    viewInfo.subresourceRange.baseMipLevel = 0;
    viewInfo.subresourceRange.levelCount = 1;
    viewInfo.subresourceRange.baseArrayLayer = 0;
    viewInfo.subresourceRange.layerCount = 1;

    if (vkCreateImageView(vk_context.device, &viewInfo, nullptr,
                          &vk_context.swapchainImageViews[i]) != VK_SUCCESS) {
      throw std::runtime_error("Failed to create image view");
    }
  }

  vk_context.swapchainExtent = extent;
  vk_context.swapchainImageFormat = surfaceFormat.format;
}

/**
 * @brief Creates the Vulkan render pass.
 */
void createRenderPass(VulkanContext &vk_context) {
  VkAttachmentDescription colorAttachment = {};
  colorAttachment.format = vk_context.swapchainImageFormat;
  colorAttachment.samples = VK_SAMPLE_COUNT_1_BIT;
  colorAttachment.loadOp = VK_ATTACHMENT_LOAD_OP_CLEAR;
  colorAttachment.storeOp = VK_ATTACHMENT_STORE_OP_STORE;
  colorAttachment.stencilLoadOp = VK_ATTACHMENT_LOAD_OP_DONT_CARE;
  colorAttachment.stencilStoreOp = VK_ATTACHMENT_STORE_OP_DONT_CARE;
  colorAttachment.initialLayout = VK_IMAGE_LAYOUT_UNDEFINED;
  colorAttachment.finalLayout = VK_IMAGE_LAYOUT_PRESENT_SRC_KHR;

  VkAttachmentReference colorAttachmentRef = {};
  colorAttachmentRef.attachment = 0;
  colorAttachmentRef.layout = VK_IMAGE_LAYOUT_COLOR_ATTACHMENT_OPTIMAL;

  VkSubpassDescription subpass = {};
  subpass.pipelineBindPoint = VK_PIPELINE_BIND_POINT_GRAPHICS;
  subpass.colorAttachmentCount = 1;
  subpass.pColorAttachments = &colorAttachmentRef;

  VkRenderPassCreateInfo renderPassInfo = {};
  renderPassInfo.sType = VK_STRUCTURE_TYPE_RENDER_PASS_CREATE_INFO;
  renderPassInfo.attachmentCount = 1;
  renderPassInfo.pAttachments = &colorAttachment;
  renderPassInfo.subpassCount = 1;
  renderPassInfo.pSubpasses = &subpass;

  if (vkCreateRenderPass(vk_context.device, &renderPassInfo, nullptr,
                         &vk_context.renderPass) != VK_SUCCESS) {
    throw std::runtime_error("Failed to create render pass");
  }
}

/**
 * @brief Creates the Vulkan framebuffers.
 */
void createFramebuffers(VulkanContext &vk_context) {
  vk_context.framebuffers.resize(vk_context.swapchainImageViews.size());
  for (size_t i = 0; i < vk_context.swapchainImageViews.size(); i++) {
    VkImageView attachments[] = {vk_context.swapchainImageViews[i]};

    VkFramebufferCreateInfo framebufferInfo = {};
    framebufferInfo.sType = VK_STRUCTURE_TYPE_FRAMEBUFFER_CREATE_INFO;
    framebufferInfo.renderPass = vk_context.renderPass;
    framebufferInfo.attachmentCount = 1;
    framebufferInfo.pAttachments = attachments;
    framebufferInfo.width = vk_context.swapchainExtent.width;
    framebufferInfo.height = vk_context.swapchainExtent.height;
    framebufferInfo.layers = 1;

    if (vkCreateFramebuffer(vk_context.device, &framebufferInfo, nullptr,
                            &vk_context.framebuffers[i]) != VK_SUCCESS) {
      throw std::runtime_error("Failed to create framebuffer");
    }
  }
}

/**
 * @brief Creates the Vulkan descriptor pool for ImGui.
 */
void createDescriptorPool(VulkanContext &vk_context) {
  VkDescriptorPoolSize poolSizes[] = {
      {VK_DESCRIPTOR_TYPE_SAMPLER, 1000},
      {VK_DESCRIPTOR_TYPE_COMBINED_IMAGE_SAMPLER, 1000},
      {VK_DESCRIPTOR_TYPE_SAMPLED_IMAGE, 1000},
      {VK_DESCRIPTOR_TYPE_STORAGE_IMAGE, 1000},
      {VK_DESCRIPTOR_TYPE_UNIFORM_TEXEL_BUFFER, 1000},
      {VK_DESCRIPTOR_TYPE_STORAGE_TEXEL_BUFFER, 1000},
      {VK_DESCRIPTOR_TYPE_UNIFORM_BUFFER, 1000},
      {VK_DESCRIPTOR_TYPE_STORAGE_BUFFER, 1000},
      {VK_DESCRIPTOR_TYPE_UNIFORM_BUFFER_DYNAMIC, 1000},
      {VK_DESCRIPTOR_TYPE_STORAGE_BUFFER_DYNAMIC, 1000},
      {VK_DESCRIPTOR_TYPE_INPUT_ATTACHMENT, 1000}};

  VkDescriptorPoolCreateInfo poolInfo = {};
  poolInfo.sType = VK_STRUCTURE_TYPE_DESCRIPTOR_POOL_CREATE_INFO;
  poolInfo.flags = VK_DESCRIPTOR_POOL_CREATE_FREE_DESCRIPTOR_SET_BIT;
  poolInfo.maxSets = 1000 * IM_ARRAYSIZE(poolSizes);
  poolInfo.poolSizeCount = IM_ARRAYSIZE(poolSizes);
  poolInfo.pPoolSizes = poolSizes;

  if (vkCreateDescriptorPool(vk_context.device, &poolInfo, nullptr,
                             &vk_context.descriptorPool) != VK_SUCCESS) {
    throw std::runtime_error("Failed to create descriptor pool");
  }
}

/**
 * @brief Cleans up swapchain-related resources.
 */
void cleanupSwapchain(VulkanContext &vk_context) {
  for (auto framebuffer : vk_context.framebuffers) {
    vkDestroyFramebuffer(vk_context.device, framebuffer, nullptr);
  }
  for (auto imageView : vk_context.swapchainImageViews) {
    vkDestroyImageView(vk_context.device, imageView, nullptr);
  }
  vkDestroySwapchainKHR(vk_context.device, vk_context.swapchain, nullptr);
}

/**
 * @brief Cleans up all Vulkan resources.
 */
void cleanupVulkan(VulkanContext &vk_context) {
  vkDeviceWaitIdle(vk_context.device);
  cleanupSwapchain(vk_context);
  vkDestroyRenderPass(vk_context.device, vk_context.renderPass, nullptr);
  vkDestroyDescriptorPool(vk_context.device, vk_context.descriptorPool,
                          nullptr);
  vkDestroyCommandPool(vk_context.device, vk_context.commandPool, nullptr);
  vkDestroyFence(vk_context.device, vk_context.inFlightFence, nullptr);
  vkDestroySemaphore(vk_context.device, vk_context.renderFinishedSemaphore,
                     nullptr);
  vkDestroySemaphore(vk_context.device, vk_context.imageAvailableSemaphore,
                     nullptr);
  vkDestroyDevice(vk_context.device, nullptr);
  vkDestroySurfaceKHR(vk_context.instance, vk_context.surface, nullptr);
  vkDestroyInstance(vk_context.instance, nullptr);
}

int main() {
#ifdef _WIN32
  // Enable Per-Monitor V2 DPI awareness for crisp rendering on high-DPI
  // displays
  EnablePerMonitorV2DpiAwareness();
#endif

  spdlog::set_level(spdlog::level::info);
  spdlog::info("Starting PS4 Emulator");
  LoadSettings();
  std::unique_ptr<PS4Emulator> emulator;
  VulkanContext vk_context;

  try {
    if (SDL_Init(SDL_INIT_VIDEO | SDL_INIT_EVENTS | SDL_INIT_GAMECONTROLLER) !=
        0) {
      spdlog::error("SDL_Init failed: {}", SDL_GetError());
      return EXIT_FAILURE;
    }

#ifdef SDL_HINT_IME_SHOW_UI
    SDL_SetHint(SDL_HINT_IME_SHOW_UI, "1");
#endif

    std::unique_ptr<SDL_Window, void (*)(SDL_Window *)> window(
        SDL_CreateWindow(kWindowTitle, SDL_WINDOWPOS_CENTERED,
                         SDL_WINDOWPOS_CENTERED, kDefaultWidth, kDefaultHeight,
                         SDL_WINDOW_SHOWN | SDL_WINDOW_RESIZABLE |
                             SDL_WINDOW_VULKAN | SDL_WINDOW_ALLOW_HIGHDPI),
        [](SDL_Window *w) {
          if (w)
            SDL_DestroyWindow(w);
        });
    if (!window) {
      spdlog::error("SDL_CreateWindow failed: {}", SDL_GetError());
      return EXIT_FAILURE;
    }
    {
      std::shared_lock<std::shared_mutex> lock(settingsMutex);
      if (settings.fullscreen) {
        SDL_SetWindowFullscreen(window.get(), SDL_WINDOW_FULLSCREEN_DESKTOP);
      }
      SDL_SetWindowSize(window.get(), settings.width, settings.height);
      std::strncpy(gamePathBuf, settings.last_game_path.c_str(),
                   sizeof(gamePathBuf));
    }

    unsigned num_extensions;
    if (!SDL_Vulkan_GetInstanceExtensions(nullptr, &num_extensions, nullptr)) {
      spdlog::error("Vulkan not supported by SDL: {}", SDL_GetError());
      throw std::runtime_error("Vulkan not supported");
    }
    std::vector<const char *> extensions(num_extensions);
    SDL_Vulkan_GetInstanceExtensions(window.get(), &num_extensions,
                                     extensions.data());
    extensions.push_back(VK_EXT_DEBUG_UTILS_EXTENSION_NAME);

    VkApplicationInfo app_info = {.sType = VK_STRUCTURE_TYPE_APPLICATION_INFO,
                                  .pApplicationName = "PS4 Emulator",
                                  .applicationVersion =
                                      VK_MAKE_VERSION(1, 0, 0),
                                  .pEngineName = "Custom",
                                  .engineVersion = VK_MAKE_VERSION(1, 0, 0),
                                  .apiVersion = VK_API_VERSION_1_0};

    VkInstanceCreateInfo create_info = {
        .sType = VK_STRUCTURE_TYPE_INSTANCE_CREATE_INFO,
        .pApplicationInfo = &app_info,
        .enabledLayerCount = 0,
        .enabledExtensionCount = static_cast<uint32_t>(extensions.size()),
        .ppEnabledExtensionNames = extensions.data()};

    if (vkCreateInstance(&create_info, nullptr, &vk_context.instance) !=
        VK_SUCCESS) {
      spdlog::error("Failed to create Vulkan instance");
      throw std::runtime_error("Vulkan instance creation failed");
    }

    if (!SDL_Vulkan_CreateSurface(window.get(), vk_context.instance,
                                  &vk_context.surface)) {
      spdlog::error("Failed to create Vulkan surface: {}", SDL_GetError());
      throw std::runtime_error("Vulkan surface creation failed");
    }

    uint32_t device_count = 0;
    if (vkEnumeratePhysicalDevices(vk_context.instance, &device_count,
                                   nullptr) != VK_SUCCESS ||
        device_count == 0) {
      spdlog::error("Failed to find GPUs with Vulkan support");
      throw std::runtime_error("No Vulkan-compatible GPUs found");
    }
    std::vector<VkPhysicalDevice> devices(device_count);
    vkEnumeratePhysicalDevices(vk_context.instance, &device_count,
                               devices.data());

    for (const auto &device : devices) {
      uint32_t queue_family_count = 0;
      vkGetPhysicalDeviceQueueFamilyProperties(device, &queue_family_count,
                                               nullptr);
      std::vector<VkQueueFamilyProperties> queue_families(queue_family_count);
      vkGetPhysicalDeviceQueueFamilyProperties(device, &queue_family_count,
                                               queue_families.data());
      for (uint32_t i = 0; i < queue_families.size(); i++) {
        VkBool32 present_support = false;
        vkGetPhysicalDeviceSurfaceSupportKHR(device, i, vk_context.surface,
                                             &present_support);
        if (queue_families[i].queueFlags & VK_QUEUE_GRAPHICS_BIT &&
            present_support) {
          vk_context.physicalDevice = device;
          vk_context.graphicsQueueFamily = i;
          vk_context.presentQueueFamily = i; // Set present queue family
          break;
        }
      }
      if (vk_context.physicalDevice)
        break;
    }
    if (!vk_context.physicalDevice) {
      spdlog::error("Failed to find a suitable GPU");
      throw std::runtime_error("No suitable GPU found");
    }

    float queue_priority = 1.0f;
    VkDeviceQueueCreateInfo queue_info = {
        .sType = VK_STRUCTURE_TYPE_DEVICE_QUEUE_CREATE_INFO,
        .queueFamilyIndex = vk_context.graphicsQueueFamily,
        .queueCount = 1,
        .pQueuePriorities = &queue_priority};

    VkPhysicalDeviceFeatures device_features = {};
    std::vector<const char *> device_extensions = {
        VK_KHR_SWAPCHAIN_EXTENSION_NAME};

    VkDeviceCreateInfo device_create_info = {
        .sType = VK_STRUCTURE_TYPE_DEVICE_CREATE_INFO,
        .queueCreateInfoCount = 1,
        .pQueueCreateInfos = &queue_info,
        .enabledExtensionCount =
            static_cast<uint32_t>(device_extensions.size()),
        .ppEnabledExtensionNames = device_extensions.data(),
        .pEnabledFeatures = &device_features};

    if (vkCreateDevice(vk_context.physicalDevice, &device_create_info, nullptr,
                       &vk_context.device) != VK_SUCCESS) {
      spdlog::error("Failed to create logical device");
      throw std::runtime_error("Vulkan device creation failed");
    }

    vkGetDeviceQueue(vk_context.device, vk_context.graphicsQueueFamily, 0,
                     &vk_context.graphicsQueue);
    vkGetDeviceQueue(vk_context.device, vk_context.presentQueueFamily, 0,
                     &vk_context.presentQueue);

    VkCommandPoolCreateInfo pool_info = {
        .sType = VK_STRUCTURE_TYPE_COMMAND_POOL_CREATE_INFO,
        .flags = VK_COMMAND_POOL_CREATE_RESET_COMMAND_BUFFER_BIT,
        .queueFamilyIndex = vk_context.graphicsQueueFamily};

    if (vkCreateCommandPool(vk_context.device, &pool_info, nullptr,
                            &vk_context.commandPool) != VK_SUCCESS) {
      spdlog::error("Failed to create command pool");
      throw std::runtime_error("Vulkan command pool creation failed");
    }

    createSwapchain(vk_context, window.get());
    createRenderPass(vk_context);
    createFramebuffers(vk_context);
    createDescriptorPool(vk_context);

    VkSemaphoreCreateInfo semaphoreInfo = {
        VK_STRUCTURE_TYPE_SEMAPHORE_CREATE_INFO};
    if (vkCreateSemaphore(vk_context.device, &semaphoreInfo, nullptr,
                          &vk_context.imageAvailableSemaphore) != VK_SUCCESS ||
        vkCreateSemaphore(vk_context.device, &semaphoreInfo, nullptr,
                          &vk_context.renderFinishedSemaphore) != VK_SUCCESS) {
      throw std::runtime_error("Failed to create semaphores");
    }

    VkFenceCreateInfo fenceInfo = {VK_STRUCTURE_TYPE_FENCE_CREATE_INFO, nullptr,
                                   VK_FENCE_CREATE_SIGNALED_BIT};
    if (vkCreateFence(vk_context.device, &fenceInfo, nullptr,
                      &vk_context.inFlightFence) != VK_SUCCESS) {
      throw std::runtime_error("Failed to create fence");
    }

    emulator = std::make_unique<PS4Emulator>();

    auto emulatorInitFuture = std::async(std::launch::async, [&]() {
      spdlog::info("Initializing PS4 emulator asynchronously");
      try {
        bool result = emulator->Initialize(window.get(), &vk_context);
        return result;
      } catch (const std::exception &e) {
        spdlog::error("Emulator initialization failed: {}", e.what());
        return false;
      }
    });

    bool emulatorInitDone = false;
    bool emulatorInitSuccess = false;
    std::unique_ptr<InputManager> inputManager;
    std::future<bool> inputInitFuture;

    IMGUI_CHECKVERSION();
    ImGui::CreateContext();
    ImGuiIO &io = ImGui::GetIO();

    // Simple default font setup (temporary for testing)
    io.Fonts->Clear();
    ImFont *font = io.Fonts->AddFontDefault();
    io.FontDefault = font;
    io.Fonts->Build();

    ImGui::StyleColorsDark();
    ImGuiStyle &style = ImGui::GetStyle();
    style.Colors[ImGuiCol_WindowBg] = ImVec4(0.12f, 0.12f, 0.12f, 1.00f);
    style.Colors[ImGuiCol_Header] = ImVec4(0.062f, 0.439f, 0.812f, 1.00f);
    style.Colors[ImGuiCol_HeaderHovered] =
        ImVec4(0.082f, 0.518f, 0.910f, 1.00f);
    style.Colors[ImGuiCol_HeaderActive] = ImVec4(0.082f, 0.518f, 0.910f, 1.00f);
    style.Colors[ImGuiCol_Button] = ImVec4(0.15f, 0.15f, 0.15f, 1.00f);
    style.Colors[ImGuiCol_ButtonHovered] =
        ImVec4(0.062f, 0.439f, 0.812f, 1.00f);
    style.Colors[ImGuiCol_ButtonActive] = ImVec4(0.082f, 0.518f, 0.910f, 1.00f);
    style.Colors[ImGuiCol_FrameBg] = ImVec4(0.10f, 0.10f, 0.10f, 1.00f);
    style.Colors[ImGuiCol_FrameBgHovered] = ImVec4(0.15f, 0.15f, 0.20f, 1.00f);
    style.Colors[ImGuiCol_FrameBgActive] = ImVec4(0.15f, 0.15f, 0.20f, 1.00f);
    style.Colors[ImGuiCol_TitleBg] = ImVec4(0.08f, 0.08f, 0.10f, 1.00f);
    style.Colors[ImGuiCol_TitleBgActive] = ImVec4(0.10f, 0.10f, 0.12f, 1.00f);
    style.WindowRounding = 4.0f;
    style.FrameRounding = 3.0f;
    style.GrabRounding = 3.0f;
    style.ScrollbarSize = 14.0f;
    style.FrameBorderSize = 1.0f;

    // Calculate DPI scale factor for font sizing
    // Get window DPI information
    int display_index = SDL_GetWindowDisplayIndex(window.get());
    float ddpi, hdpi, vdpi;
    float dpi_scale = 1.0f;

    if (SDL_GetDisplayDPI(display_index, &ddpi, &hdpi, &vdpi) == 0) {
      // Use diagonal DPI for scaling, 96 DPI is standard Windows DPI
      dpi_scale = ddpi / 96.0f;
      spdlog::info("Display DPI: {:.1f}, scaling factor: {:.2f}", ddpi,
                   dpi_scale);
    } else {
      spdlog::warn("Failed to get display DPI, using default scaling");
    }

    // Clamp DPI scale to reasonable bounds
    dpi_scale = std::clamp(dpi_scale, 0.75f, 3.0f);

    // Fixed base font size in logical pixels - scale with both DPI and user UI
    // scale
    float base_font_size = 16.0f * settings.ui_scale;
    float final_scale = dpi_scale * settings.ui_scale;

    // Use ImGui's default font with better configuration
    ImFontConfig font_cfg;
    font_cfg.SizePixels = base_font_size; // set font size
    font_cfg.OversampleH = 3;
    font_cfg.OversampleV = 2;
    font_cfg.RasterizerDensity = final_scale;

#ifdef IMGUI_ENABLE_FREETYPE
    // Enable FreeType builder flags for better text rendering quality
    font_cfg.FontBuilderFlags = ImGuiFreeTypeBuilderFlags_LightHinting;
    spdlog::info("Using FreeType font renderer with LightHinting and "
                 "RasterizerDensity {:.2f} (DPI: {:.2f} * UI Scale: {:.2f})",
                 final_scale, dpi_scale, settings.ui_scale);
#else
    spdlog::info("Using STB font renderer with oversampling {}x{}",
                 font_cfg.OversampleH, font_cfg.OversampleV);
#endif

    // Try to load a system font - prioritize available fonts
    ImFont *primaryFont = nullptr;
    const char *fontPaths[] = {
        "d:/sss/Roboto-Black.ttf", "C:/Windows/Fonts/segoeui.ttf",
        "C:/Windows/Fonts/arial.ttf", "C:/Windows/Fonts/calibri.ttf", nullptr};

    for (const char **path = fontPaths; *path != nullptr; ++path) {
      if (std::filesystem::exists(*path)) {
        primaryFont =
            io.Fonts->AddFontFromFileTTF(*path, base_font_size, &font_cfg);
        if (primaryFont) {
          spdlog::info("Loaded font: {} at {:.1f}px with scale {:.2f}", *path,
                       base_font_size, final_scale);
          break;
        }
      }
    }

    if (!primaryFont) {
      spdlog::error("Failed to load any TTF fonts. Please ensure "
                    "Roboto-Black.ttf is in the proper location.");
      throw std::runtime_error(
          "Font loading failed - no usable fonts available");
    }

    // Set the font to use
    io.FontDefault = primaryFont;

    // Set display framebuffer scale for high-DPI displays
    io.DisplayFramebufferScale = ImVec2(final_scale, final_scale);

    io.Fonts->Build();

    // Scale ImGui style to match combined DPI and UI scaling for proper UI
    // element sizing
    style.ScaleAllSizes(final_scale);

    spdlog::info("Font atlas built: {}x{} pixels, {} fonts loaded",
                 io.Fonts->TexWidth, io.Fonts->TexHeight, io.Fonts->Fonts.Size);
    spdlog::info(
        "ImGui style scaled by factor {:.2f} (DPI: {:.2f} * UI Scale: {:.2f})",
        final_scale, dpi_scale, settings.ui_scale);

    if (!ImGui_ImplSDL2_InitForVulkan(window.get())) {
      spdlog::error("ImGui_ImplSDL2_InitForVulkan failed");
      throw std::runtime_error("ImGui SDL2 initialization failed");
    }

    ImGui_ImplVulkan_InitInfo vk_init_info = {};
    vk_init_info.Instance = vk_context.instance;
    vk_init_info.PhysicalDevice = vk_context.physicalDevice;
    vk_init_info.Device = vk_context.device;
    vk_init_info.QueueFamily = vk_context.graphicsQueueFamily;
    vk_init_info.Queue = vk_context.graphicsQueue;
    vk_init_info.DescriptorPool = vk_context.descriptorPool;
    vk_init_info.MinImageCount =
        static_cast<uint32_t>(vk_context.swapchainImages.size());
    vk_init_info.ImageCount =
        static_cast<uint32_t>(vk_context.swapchainImages.size());
    vk_init_info.MSAASamples = VK_SAMPLE_COUNT_1_BIT;
    vk_init_info.CheckVkResultFn = check_vk_result;
    vk_init_info.RenderPass = vk_context.renderPass;

    if (!ImGui_ImplVulkan_Init(&vk_init_info)) {
      spdlog::error("ImGui_ImplVulkan_Init failed");
      throw std::runtime_error("ImGui Vulkan initialization failed");
    }

    VkCommandBufferAllocateInfo alloc_info = {
        .sType = VK_STRUCTURE_TYPE_COMMAND_BUFFER_ALLOCATE_INFO,
        .commandPool = vk_context.commandPool,
        .level = VK_COMMAND_BUFFER_LEVEL_PRIMARY,
        .commandBufferCount = 1};

    VkCommandBuffer command_buffer;
    if (vkAllocateCommandBuffers(vk_context.device, &alloc_info,
                                 &command_buffer) != VK_SUCCESS) {
      spdlog::error("Failed to allocate command buffer for font upload");
      throw std::runtime_error("Vulkan command buffer allocation failed");
    }

    VkCommandBufferBeginInfo begin_info = {
        .sType = VK_STRUCTURE_TYPE_COMMAND_BUFFER_BEGIN_INFO,
        .flags = VK_COMMAND_BUFFER_USAGE_ONE_TIME_SUBMIT_BIT};

    if (vkBeginCommandBuffer(command_buffer, &begin_info) != VK_SUCCESS ||
        !ImGui_ImplVulkan_CreateFontsTexture() ||
        vkEndCommandBuffer(command_buffer) != VK_SUCCESS) {
      spdlog::error("Failed to upload ImGui fonts");
      throw std::runtime_error("ImGui font upload failed");
    }

    VkSubmitInfo submit_info = {.sType = VK_STRUCTURE_TYPE_SUBMIT_INFO,
                                .commandBufferCount = 1,
                                .pCommandBuffers = &command_buffer};

    if (vkQueueSubmit(vk_context.graphicsQueue, 1, &submit_info,
                      VK_NULL_HANDLE) != VK_SUCCESS) {
      spdlog::error("Failed to submit font upload command buffer");
      throw std::runtime_error("Vulkan font upload submission failed");
    }

    vkDeviceWaitIdle(vk_context.device);
    ImGui_ImplVulkan_DestroyFontsTexture();
    vkFreeCommandBuffers(vk_context.device, vk_context.commandPool, 1,
                         &command_buffer);

    VkCommandBuffer render_command_buffer;
    if (vkAllocateCommandBuffers(vk_context.device, &alloc_info,
                                 &render_command_buffer) != VK_SUCCESS) {
      spdlog::error("Failed to allocate render command buffer");
      throw std::runtime_error(
          "Vulkan render command buffer allocation failed");
    }

    bool quit = false;
    SDL_Event e;
    auto lastFrame = std::chrono::steady_clock::now();
    const auto frameTime = std::chrono::milliseconds(16);
    bool swapchain_rebuild = false;
    ImVec4 clear_color = ImVec4(0.12f, 0.12f, 0.12f, 1.00f);
    bool show_demo_window = false;
    bool show_another_window = false;

    while (!quit) {
      if (!emulatorInitDone) {
        if (emulatorInitFuture.wait_for(std::chrono::milliseconds(0)) ==
            std::future_status::ready) {
          emulatorInitSuccess = emulatorInitFuture.get();
          emulatorInitDone = true;
          if (emulatorInitSuccess) {
            spdlog::info("PS4 emulator initialized");
            inputManager = std::make_unique<InputManager>(
                emulator->GetControllerManager());
            inputInitFuture = std::async(std::launch::async, [&]() {
              try {
                return inputManager->Initialize();
              } catch (const std::exception &e) {
                spdlog::error("InputManager initialization failed: {}",
                              e.what());
                return false;
              }
            });
          } else {
            spdlog::error("Failed to initialize PS4 emulator");
            quit = true;
          }
        }
      }

      if (inputManager)
        inputManager->PollEvents();

      while (SDL_PollEvent(&e)) {
        ImGui_ImplSDL2_ProcessEvent(&e);

        // Let ImGui handle input first
        ImGuiIO &io = ImGui::GetIO();
        if (io.WantCaptureMouse || io.WantCaptureKeyboard) {
          // ImGui wants to handle this input, don't pass to game
          if (e.type == SDL_QUIT)
            quit = true;
          continue;
        }

        if (e.type == SDL_QUIT)
          quit = true;
        if (e.type == SDL_WINDOWEVENT &&
            e.window.event == SDL_WINDOWEVENT_CLOSE &&
            e.window.windowID == SDL_GetWindowID(window.get())) {
          quit = true;
        }

        // Handle controller device events centrally to prevent SDL race
        // conditions
        if (emulatorInitSuccess && emulator) {
          auto &controllerManager = emulator->GetControllerManager();
          if (e.type == SDL_CONTROLLERDEVICEADDED) {
            controllerManager.HandleControllerDeviceAdded(e.cdevice.which);
          } else if (e.type == SDL_CONTROLLERDEVICEREMOVED) {
            controllerManager.HandleControllerDeviceRemoved(e.cdevice.which);
          }
        }

        // Pass other events to input manager only if ImGui doesn't want them
        if (inputManager && !io.WantCaptureMouse && !io.WantCaptureKeyboard) {
          // Process game input here if needed
        }
      }

      ImGui_ImplVulkan_NewFrame();
      ImGui_ImplSDL2_NewFrame();
      ImGui::NewFrame();

      if (!emulatorInitDone) {
        ImGui::Begin("Initializing...");
        ImGui::Text("PS4 emulator is loading, please wait...");
        ImGui::End();
        ImGui::Render();
        ImDrawData *draw_data = ImGui::GetDrawData();
        uint32_t image_index = 0;
        renderFrame(vk_context, render_command_buffer, draw_data, image_index,
                    swapchain_rebuild, quit, *emulator);
        continue;
      }

      if (inputInitFuture.valid() &&
          inputInitFuture.wait_for(std::chrono::milliseconds(0)) ==
              std::future_status::ready) {
        if (!inputInitFuture.get()) {
          spdlog::error("Failed to initialize InputManager");
          quit = true;
        }
      }

      if (SDL_GetWindowFlags(window.get()) & SDL_WINDOW_MINIMIZED) {
        SDL_Delay(10);
        continue;
      }

      int fb_width, fb_height;
      SDL_GetWindowSize(window.get(), &fb_width, &fb_height);
      if (fb_width > 0 && fb_height > 0 &&
          (swapchain_rebuild || vk_context.swapchainExtent.width != fb_width ||
           vk_context.swapchainExtent.height != fb_height)) {
        swapchain_rebuild = true;
      }

      if (swapchain_rebuild) {
        swapchain_rebuild = false;
        vkDeviceWaitIdle(vk_context.device);
        cleanupSwapchain(vk_context);
        createSwapchain(vk_context, window.get());
        createFramebuffers(vk_context);
        ImGuiIO &io = ImGui::GetIO();
        io.DisplaySize =
            ImVec2(static_cast<float>(vk_context.swapchainExtent.width),
                   static_cast<float>(vk_context.swapchainExtent.height));
      }

      if (ImGui::BeginMainMenuBar()) {
        if (ImGui::BeginMenu("File")) {
          if (ImGui::MenuItem("Game Browser"))
            show_game_browser = true;
          if (ImGui::MenuItem("Load Game"))
            show_load_game_window = true;
          ImGui::Separator();
          if (ImGui::MenuItem("Install PKG File"))
            show_install_pkg_window = true;
          if (ImGui::MenuItem("Set Game Directory"))
            show_set_game_directory_window = true;
          ImGui::Separator();
          if (ImGui::MenuItem("Save State"))
            show_save_state_window = true;
          if (ImGui::MenuItem("Load State"))
            show_load_state_window = true;
          ImGui::Separator();
          if (ImGui::MenuItem("Save Settings")) {
            std::unique_lock<std::shared_mutex> lock(settingsMutex);
            settings.last_game_path = gamePathBuf;
            settings.width = fb_width;
            settings.height = fb_height;
            settings.fullscreen = SDL_GetWindowFlags(window.get()) &
                                  SDL_WINDOW_FULLSCREEN_DESKTOP;
            SaveSettings();
          }
          if (ImGui::MenuItem("Load Settings")) {
            LoadSettings();
            std::shared_lock<std::shared_mutex> lock(settingsMutex);
            SDL_SetWindowSize(window.get(), settings.width, settings.height);
            SDL_SetWindowFullscreen(
                window.get(),
                settings.fullscreen ? SDL_WINDOW_FULLSCREEN_DESKTOP : 0);
            std::strncpy(gamePathBuf, settings.last_game_path.c_str(),
                         sizeof(gamePathBuf));
          }
          if (ImGui::BeginMenu("Recent Games")) {
            std::shared_lock<std::shared_mutex> lock(settingsMutex);
            for (const auto &path : settings.recent_games) {
              if (ImGui::MenuItem(path.c_str())) {
                std::strncpy(gamePathBuf, path.c_str(), sizeof(gamePathBuf));
                if (emulator->LoadGame(path)) {
                  lock.unlock();
                  std::unique_lock<std::shared_mutex> wlock(settingsMutex);
                  settings.recent_games.erase(
                      std::remove(settings.recent_games.begin(),
                                  settings.recent_games.end(), path),
                      settings.recent_games.end());
                  settings.recent_games.insert(settings.recent_games.begin(),
                                               path);
                  if (settings.recent_games.size() > 5)
                    settings.recent_games.pop_back();
                  SaveSettings();
                }
              }
            }
            ImGui::EndMenu();
          }
          if (ImGui::MenuItem("Filesystem"))
            show_filesystem_window = true;
          if (ImGui::MenuItem("Diagnostics"))
            show_diagnostics_window = true;
          if (ImGui::MenuItem("Input"))
            show_input_window = true;
          if (ImGui::MenuItem("Exit"))
            quit = true;
          ImGui::EndMenu();
        }
        if (ImGui::BeginMenu("Emulation")) {
          if (ImGui::MenuItem("Start"))
            emulator->Start();
          if (ImGui::MenuItem("Pause"))
            emulator->Pause();
          if (ImGui::MenuItem("Stop"))
            emulator->Stop();
          ImGui::SliderFloat("Speed", &settings.emulation_speed, 0.1f, 3.0f);
          ImGui::Checkbox("Audio", &settings.audio_enabled);
          emulator->GetAudio().SetEnabled(settings.audio_enabled);
          ImGui::EndMenu();
        }
        if (ImGui::BeginMenu("Debug")) {
          ImGui::Checkbox("Show Demo Window", &show_demo_window);
          ImGui::Checkbox("Font Debug Window", &show_font_debug_window);
          ImGui::EndMenu();
        }
        if (ImGui::BeginMenu("Settings")) {
          if (ImGui::MenuItem("Display Settings"))
            show_display_settings = true;
          if (ImGui::MenuItem("Audio Settings"))
            show_audio_settings = true;
          if (ImGui::MenuItem("CPU Settings"))
            show_cpu_settings = true;
          if (ImGui::MenuItem("GPU Settings"))
            show_gpu_settings = true;
          if (ImGui::MenuItem("Memory Settings"))
            show_memory_settings = true;
          if (ImGui::MenuItem("Filesystem Settings"))
            show_filesystem_settings = true;
          if (ImGui::MenuItem("Debug Settings"))
            show_debug_settings = true;
          if (ImGui::MenuItem("Input Settings"))
            show_input_settings = true;
          if (ImGui::MenuItem("Network Settings"))
            show_network_settings = true;
          if (ImGui::MenuItem("Compatibility Settings"))
            show_compatibility_settings = true;
          if (ImGui::MenuItem("Advanced Settings"))
            show_advanced_settings = true;
          ImGui::EndMenu();
        }
        ImGui::EndMainMenuBar();
      }

      // Main PS4 Emulator Window - Simple and Functional
      ImGui::SetNextWindowPos(ImVec2(50, 50), ImGuiCond_FirstUseEver);
      ImGui::SetNextWindowSize(ImVec2(800, 600), ImGuiCond_FirstUseEver);

      ImGui::Begin("PS4 Emulator - shadPS4 Style", nullptr,
                   ImGuiWindowFlags_MenuBar);

      // Menu bar inside the window
      if (ImGui::BeginMenuBar()) {
        if (ImGui::BeginMenu("File")) {
          if (ImGui::MenuItem("Load Game"))
            show_load_game_window = true;
          if (ImGui::MenuItem("Game Browser"))
            show_game_browser = true;
          ImGui::Separator();
          if (ImGui::MenuItem("Exit"))
            quit = true;
          ImGui::EndMenu();
        }
        if (ImGui::BeginMenu("Emulation")) {
          if (ImGui::MenuItem("Start"))
            emulator->Start();
          if (ImGui::MenuItem("Pause"))
            emulator->Pause();
          if (ImGui::MenuItem("Stop"))
            emulator->Stop();
          ImGui::EndMenu();
        }
        if (ImGui::BeginMenu("Settings")) {
          if (ImGui::MenuItem("Display"))
            show_display_settings = true;
          if (ImGui::MenuItem("Audio"))
            show_audio_settings = true;
          if (ImGui::MenuItem("CPU"))
            show_cpu_settings = true;
          if (ImGui::MenuItem("GPU"))
            show_gpu_settings = true;
          ImGui::EndMenu();
        }
        ImGui::EndMenuBar();
      }

      // Large, clickable buttons
      ImGui::PushStyleVar(ImGuiStyleVar_FramePadding, ImVec2(15, 8));

      ImGui::Text("Game Management:");
      ImGui::Separator();

      if (ImGui::Button("Load Game File", ImVec2(180, 40))) {
        show_load_game_window = true;
      }
      ImGui::SameLine();
      if (ImGui::Button("Browse Games", ImVec2(180, 40))) {
        show_game_browser = true;
      }

      ImGui::Spacing();
      ImGui::Text("Emulation Control:");
      ImGui::Separator();

      if (ImGui::Button("Start Emulation", ImVec2(120, 35))) {
        emulator->Start();
      }
      ImGui::SameLine();
      if (ImGui::Button("Pause", ImVec2(120, 35))) {
        emulator->Pause();
      }
      ImGui::SameLine();
      if (ImGui::Button("Stop", ImVec2(120, 35))) {
        emulator->Stop();
      }

      ImGui::PopStyleVar();

      ImGui::Spacing();
      ImGui::Text("Quick Settings:");
      ImGui::Separator();

      // Quick settings in tabs
      if (ImGui::BeginTabBar("SettingsTabs")) {
        if (ImGui::BeginTabItem("Display")) {
          ImGui::SliderInt("Width", &settings.width, 640, 3840);
          ImGui::SliderInt("Height", &settings.height, 480, 2160);
          ImGui::Checkbox("Fullscreen", &settings.fullscreen);
          ImGui::Checkbox("VSync", &settings.vsync);
          if (ImGui::Button("Apply Display Settings")) {
            SDL_SetWindowSize(window.get(), settings.width, settings.height);
            SDL_SetWindowFullscreen(
                window.get(),
                settings.fullscreen ? SDL_WINDOW_FULLSCREEN_DESKTOP : 0);
          }
          ImGui::EndTabItem();
        }

        if (ImGui::BeginTabItem("Audio")) {
          ImGui::Checkbox("Audio Enabled", &settings.audio_enabled);
          ImGui::SliderFloat("Master Volume", &settings.master_volume, 0.0f,
                             1.0f);
          ImGui::SliderFloat("SFX Volume", &settings.sfx_volume, 0.0f, 1.0f);
          ImGui::SliderFloat("Music Volume", &settings.music_volume, 0.0f,
                             1.0f);
          if (ImGui::Button("Apply Audio Settings")) {
            emulator->GetAudio().SetEnabled(settings.audio_enabled);
          }
          ImGui::EndTabItem();
        }

        if (ImGui::BeginTabItem("Performance")) {
          ImGui::SliderFloat("Emulation Speed", &settings.emulation_speed, 0.1f,
                             3.0f);
          ImGui::SliderInt("CPU Threads", &settings.cpu_thread_count, 1, 16);
          ImGui::Checkbox("JIT Compilation", &settings.jit_enabled);
          ImGui::Checkbox("SIMD Optimizations", &settings.simd_optimizations);
          ImGui::EndTabItem();
        }

        if (ImGui::BeginTabItem("Graphics")) {
          const char *backends[] = {"Vulkan", "OpenGL"};
          int current = (settings.gpu_backend == "Vulkan") ? 0 : 1;
          if (ImGui::Combo("GPU Backend", &current, backends, 2)) {
            settings.gpu_backend = backends[current];
          }
          ImGui::SliderInt("Resolution Scale", &settings.resolution_scale, 1,
                           4);
          ImGui::Checkbox("Anisotropic Filtering",
                          &settings.anisotropic_filtering);
          ImGui::Checkbox("Anti-Aliasing", &settings.anti_aliasing);
          ImGui::EndTabItem();
        }

        ImGui::EndTabBar();
      }

      ImGui::Spacing();
      ImGui::Separator();

      // Status and info
      ImGui::Text("Current Game:");
      ImGui::TextWrapped("%s", settings.last_game_path.empty()
                                   ? "No game loaded"
                                   : settings.last_game_path.c_str());

      ImGui::Spacing();
      if (ImGui::Button("Save All Settings", ImVec2(150, 30))) {
        SaveSettings();
      }
      ImGui::SameLine();
      if (ImGui::Button("Load Settings", ImVec2(150, 30))) {
        LoadSettings();
      }

      ImGui::End();

      // Game Browser Window
      if (show_game_browser) {
        ImGui::Begin("Game Browser", &show_game_browser,
                     ImGuiWindowFlags_MenuBar);

        // Static variables for async directory scanning
        static std::future<std::vector<std::string>> dirScanFuture;
        static bool dirScanInProgress = false;
        static std::string lastScannedDir = "";

        if (ImGui::BeginMenuBar()) {
          if (ImGui::BeginMenu("Actions")) {
            if (ImGui::MenuItem("Refresh Game List")) {
              game_list.clear();

              if (!settings.game_directory.empty() &&
                  std::filesystem::exists(settings.game_directory) &&
                  !dirScanInProgress && lastScannedDir != settings.game_directory) {

                dirScanInProgress = true;
                lastScannedDir = settings.game_directory;
                spdlog::info("Starting async scan of game directory: {}", settings.game_directory);

                // Launch async directory scan
                dirScanFuture = std::async(std::launch::async, [gameDir = settings.game_directory]() {
                  std::vector<std::string> foundGames;
                  try {
                    spdlog::info("Scanning game directory: {}", gameDir);
                    int file_count = 0;
                    const int MAX_FILES_TO_SCAN = 10000; // Prevent infinite loops

                    std::filesystem::recursive_directory_iterator iter(gameDir);
                    std::filesystem::recursive_directory_iterator end;

                    for (; iter != end && file_count < MAX_FILES_TO_SCAN; ++iter) {
                      try {
                        if (iter->is_regular_file()) {
                          auto ext = iter->path().extension().string();
                          std::transform(ext.begin(), ext.end(), ext.begin(), ::tolower);
                          if (ext == ".pkg" || ext == ".elf" || ext == ".bin" || ext == ".iso") {
                            foundGames.push_back(iter->path().string());
                            spdlog::debug("Found game file: {}", iter->path().string());
                          }
                        }
                        file_count++;

                        // Add periodic logging to show progress
                        if (file_count % 1000 == 0) {
                          spdlog::info("Scanned {} files so far...", file_count);
                        }
                      } catch (const std::exception &e) {
                        spdlog::warn("Error accessing file {}: {}", iter->path().string(), e.what());
                        // Continue with next file
                      }
                    }

                    if (file_count >= MAX_FILES_TO_SCAN) {
                      spdlog::warn("Stopped scanning after {} files (limit reached)", MAX_FILES_TO_SCAN);
                    }

                    spdlog::info("Game directory scan complete. Found {} games out of {} files scanned",
                                foundGames.size(), file_count);
                  } catch (const std::exception &e) {
                    spdlog::error("Failed to scan game directory: {}", e.what());
                  }
                  return foundGames;
                });
              }

              // Check if async directory scan is complete
              if (dirScanInProgress && dirScanFuture.valid()) {
                if (dirScanFuture.wait_for(std::chrono::milliseconds(0)) == std::future_status::ready) {
                  try {
                    auto foundGames = dirScanFuture.get();
                    game_list.insert(game_list.end(), foundGames.begin(), foundGames.end());
                    dirScanInProgress = false;
                    spdlog::info("Directory scan completed, added {} games to list", foundGames.size());
                  } catch (const std::exception &e) {
                    spdlog::error("Failed to get directory scan results: {}", e.what());
                    dirScanInProgress = false;
                  }
                }
              }

              // Add installed PKG games
              if (emulator) {
                try {
                  spdlog::info("Getting list of installed packages...");
                  auto installedPackages = emulator->GetPKGInstaller().ListInstalledPackages();
                  spdlog::info("Found {} installed packages", installedPackages.size());

                  for (const auto &contentId : installedPackages) {
                    spdlog::info("Processing package: {}", contentId);
                    // Check what files are actually in the installed directory
                    std::string packageDir = "/mnt/sandbox/pfsmnt/" + contentId;
                    spdlog::info("Listing files in directory: {}", packageDir);

                    std::vector<std::string> files;
                    try {
                      files = emulator->GetFilesystem().ListFiles(packageDir, false);
                      spdlog::info("Package {} contains {} files:", contentId, files.size());
                      for (const auto &file : files) {
                        spdlog::info("  - {}", file);
                      }
                    } catch (const std::exception &e) {
                      spdlog::error("Failed to list files in {}: {}", packageDir, e.what());
                      continue; // Skip this package and continue with others
                    }

                    // Look for eboot.bin or any executable file
                    bool hasEboot = false;
                    for (const auto &file : files) {
                      if (file.find("eboot.bin") != std::string::npos) {
                        hasEboot = true;
                        break;
                      }
                    }

                    if (hasEboot) {
                      game_list.push_back("[INSTALLED] " + contentId + " (eboot.bin)");
                    } else {
                      // Show the first executable-looking file
                      for (const auto &file : files) {
                        if (file.find(".bin") != std::string::npos || file.find(".elf") != std::string::npos) {
                          std::filesystem::path filePath(file);
                          std::string filename = filePath.filename().string();
                          game_list.push_back("[INSTALLED] " + contentId + " (" + filename + ")");
                          break;
                        }
                      }
                    }
                  }
                  spdlog::info("Found {} installed packages", installedPackages.size());
                } catch (const std::exception &e) {
                  spdlog::error("Failed to scan installed packages: {}", e.what());
                }
              }
            }
            if (ImGui::MenuItem("Set Game Directory")) {
              show_set_game_directory_window = true;
            }
            ImGui::EndMenu();
          }
          ImGui::EndMenuBar();
        }

        ImGui::Text("Game Directory: %s",
                    settings.game_directory.empty()
                        ? "Not set"
                        : settings.game_directory.c_str());
        ImGui::Text("Found %zu games", game_list.size());

        // Show directory scan status
        if (dirScanInProgress) {
          ImGui::TextColored(ImVec4(1.0f, 1.0f, 0.0f, 1.0f), "Scanning directory...");
        }

        ImGui::Separator();

        // Game list
        if (ImGui::BeginChild("GameList", ImVec2(0, -30))) {
          for (size_t i = 0; i < game_list.size(); ++i) {
            std::filesystem::path gamePath(game_list[i]);
            std::string displayName = gamePath.filename().string();

            if (ImGui::Selectable(displayName.c_str(),
                                  selected_game == game_list[i])) {
              selected_game = game_list[i];
              std::strncpy(gamePathBuf, selected_game.c_str(),
                           sizeof(gamePathBuf));
            }

            if (ImGui::IsItemHovered()) {
              ImGui::SetTooltip("%s", game_list[i].c_str());
            }

            // Double-click to load
            if (ImGui::IsItemHovered() && ImGui::IsMouseDoubleClicked(0)) {
              std::string gameToLoad = selected_game;

              // Handle installed games
              if (selected_game.starts_with("[INSTALLED]")) {
                // Extract content ID from "[INSTALLED] CONTENT_ID (eboot.bin)"
                size_t start = selected_game.find("] ") + 2;
                size_t end = selected_game.find(" (");
                if (start != std::string::npos && end != std::string::npos) {
                  std::string contentId = selected_game.substr(start, end - start);
                  // Use the correct path where files are installed
                  gameToLoad = "/mnt/sandbox/pfsmnt/" + contentId + "/eboot.bin";
                  spdlog::info("Loading installed game: {} -> {}", selected_game, gameToLoad);
                }
              }

              if (emulator->LoadGame(gameToLoad)) {
                std::unique_lock<std::shared_mutex> lock(settingsMutex);
                settings.last_game_path = gameToLoad;
                settings.recent_games.erase(
                    std::remove(settings.recent_games.begin(),
                                settings.recent_games.end(), gameToLoad),
                    settings.recent_games.end());
                settings.recent_games.insert(settings.recent_games.begin(),
                                             gameToLoad);
                if (settings.recent_games.size() > 10) {
                  settings.recent_games.pop_back();
                }
                SaveSettings();
                show_game_browser = false;
              }
            }
          }
        }
        ImGui::EndChild();

        ImGui::Separator();
        if (ImGui::Button("Load Selected Game") && !selected_game.empty()) {
          std::string gameToLoad = selected_game;

          // Handle installed games
          if (selected_game.starts_with("[INSTALLED]")) {
            // Extract content ID from "[INSTALLED] CONTENT_ID (eboot.bin)"
            size_t start = selected_game.find("] ") + 2;
            size_t end = selected_game.find(" (");
            if (start != std::string::npos && end != std::string::npos) {
              std::string contentId = selected_game.substr(start, end - start);
              // Use the correct path where files are installed
              gameToLoad = "/mnt/sandbox/pfsmnt/" + contentId + "/eboot.bin";
              spdlog::info("Loading installed game: {} -> {}", selected_game, gameToLoad);
            }
          }

          if (emulator->LoadGame(gameToLoad)) {
            std::unique_lock<std::shared_mutex> lock(settingsMutex);
            settings.last_game_path = gameToLoad;
            settings.recent_games.erase(
                std::remove(settings.recent_games.begin(),
                            settings.recent_games.end(), gameToLoad),
                settings.recent_games.end());
            settings.recent_games.insert(settings.recent_games.begin(),
                                         gameToLoad);
            if (settings.recent_games.size() > 10) {
              settings.recent_games.pop_back();
            }
            SaveSettings();
            show_game_browser = false;
          }
        }
        ImGui::SameLine();
        if (ImGui::Button("Close")) {
          show_game_browser = false;
        }

        ImGui::End();
      }

      if (show_load_game_window) {
        ImGui::Begin("Load Game", &show_load_game_window);
        ImGui::InputText("Game Path", gamePathBuf, sizeof(gamePathBuf));
        if (ImGui::Button("Load")) {
          std::string gamePath = gamePathBuf;
          auto loadFuture =
              std::async(std::launch::async, [&emulator, gamePath]() {
                return emulator->LoadGame(gamePath);
              });
          if (loadFuture.get()) {
            std::unique_lock<std::shared_mutex> lock(settingsMutex);
            settings.last_game_path = gamePath;
            settings.recent_games.erase(
                std::remove(settings.recent_games.begin(),
                            settings.recent_games.end(), gamePath),
                settings.recent_games.end());
            settings.recent_games.insert(settings.recent_games.begin(),
                                         gamePath);
            if (settings.recent_games.size() > 5)
              settings.recent_games.pop_back();
            SaveSettings();
          }
          show_load_game_window = false;
        }
        if (ImGui::Button("Close"))
          show_load_game_window = false;
        ImGui::End();
      }

      if (show_save_state_window) {
        ImGui::Begin("Save State", &show_save_state_window);
        ImGui::InputText("State Path", statePathBuf, sizeof(statePathBuf));
        if (ImGui::Button("Save")) {
          std::string statePath = statePathBuf;
          auto saveFuture =
              std::async(std::launch::async, [&emulator, statePath]() {
                try {
                  emulator->SaveState(statePath);
                  return true;
                } catch (const std::exception &e) {
                  spdlog::error("Failed to save state to {}: {}", statePath,
                                e.what());
                  return false;
                }
              });
          if (saveFuture.get()) {
            spdlog::info("State saved to {}", statePath);
          } else {
            spdlog::error("Failed to save state to {}", statePath);
          }
          show_save_state_window = false;
        }
        if (ImGui::Button("Close"))
          show_save_state_window = false;
        ImGui::End();
      }

      if (show_load_state_window) {
        ImGui::Begin("Load State", &show_load_state_window);
        ImGui::InputText("State Path", statePathBuf, sizeof(statePathBuf));
        if (ImGui::Button("Load")) {
          std::string statePath = statePathBuf;
          auto loadFuture =
              std::async(std::launch::async, [&emulator, statePath]() {
                try {
                  emulator->LoadState(statePath);
                  return true;
                } catch (const std::exception &e) {
                  spdlog::error("Failed to load state from {}: {}", statePath,
                                e.what());
                  return false;
                }
              });
          if (loadFuture.get()) {
            spdlog::info("State loaded from {}", statePath);
          } else {
            spdlog::error("Failed to load state from {}", statePath);
          }
          show_load_state_window = false;
        }
        if (ImGui::Button("Close"))
          show_load_state_window = false;
        ImGui::End();
      }

      if (show_filesystem_window) {
        ImGui::Begin("Filesystem", &show_filesystem_window);
        ImGui::Text("Filesystem State:");
        ImGui::TextWrapped("%s", emulator->GetFilesystem().DumpState().c_str());
        static char dirPathBuf[260] = "/app0";
        ImGui::InputText("Directory Path", dirPathBuf, sizeof(dirPathBuf));
        if (ImGui::Button("Create Directory")) {
          try {
            emulator->GetFilesystem().CreateVirtualDirectory(dirPathBuf);
            spdlog::info("Created directory {} in emulated filesystem",
                         dirPathBuf);
          } catch (const std::exception &e) {
            spdlog::error("Failed to create directory {}: {}", dirPathBuf,
                          e.what());
          }
        }
        ImGui::End();
      }

      if (show_diagnostics_window) {
        ImGui::Begin("Diagnostics", &show_diagnostics_window);
        ImGui::Text("Emulator Stats:");
        auto emuStats = emulator->GetStats();
        ImGui::Text("Instructions Executed: %llu",
                    emuStats.instructionsExecuted.load());
        ImGui::Text("Total Cycles: %llu", emuStats.totalCycles.load());
        ImGui::Text("Total Latency: %llu us", emuStats.totalLatencyUs.load());
        ImGui::Text("Memory Diagnostics:");
        for (const auto &[key, value] :
             MemoryDiagnostics::GetInstance().GetMetrics()) {
          ImGui::Text("%s: %llu", key.c_str(), value);
        }
        ImGui::Text("CPU Diagnostics:");
        for (const auto &[key, value] :
             x86_64::CPUDiagnostics::GetInstance().GetMetrics()) {
          ImGui::Text("%s: %llu", key.c_str(), value);
        }
        ImGui::Text("JIT Diagnostics:");
        for (const auto &[key, value] :
             x86_64::JITDiagnostics::GetInstance().GetMetrics()) {
          ImGui::Text("%s: %llu", key.c_str(), value);
        }
        ImGui::Text("GPU Stats:");
        auto gpuStats = emulator->GetGPU().GetStats();
        // RACE CONDITION FIX: Use .load() for atomic variables to prevent copy
        // constructor issues
        ImGui::Text("Shader Count: %llu", gpuStats.shaderCount.load());
        ImGui::Text("Draw Count: %llu", gpuStats.drawCount.load());
        ImGui::Text("Total Latency: %llu us", gpuStats.totalLatencyUs.load());
        ImGui::Text("Cache Hits: %llu", gpuStats.cacheHits.load());
        ImGui::Text("Cache Misses: %llu", gpuStats.cacheMisses.load());
        ImGui::Text("Errors: %llu", gpuStats.errorCount.load());
        ImGui::Text("Shader Translator Stats:");
        // Note: Shader translator stats are now handled internally by the GPU
        ImGui::Text("(Shader translator stats integrated into GPU stats)");
        ImGui::Text("Tile Manager Stats:");
        auto tileStats = emulator->GetTileManager().GetStats();
        // NOTE: TileManagerStats uses regular uint64_t, not atomic
        ImGui::Text("Operation Count: %llu", tileStats.operationCount);
        ImGui::Text("Total Latency: %llu us", tileStats.totalLatencyUs);
        ImGui::Text("Cache Hits: %llu", tileStats.cacheHits);
        ImGui::Text("Cache Misses: %llu", tileStats.cacheMisses);
        ImGui::Text("Errors: %llu", tileStats.errorCount);
        ImGui::Text("Command Processor Stats:");
        auto cmdStats = emulator->GetCommandProcessor().GetStats();
        // NOTE: CommandProcessorStats uses regular uint64_t, not atomic
        ImGui::Text("Packet Count: %llu", cmdStats.packetCount);
        ImGui::Text("Total Latency: %llu us", cmdStats.totalLatencyUs);
        ImGui::Text("Cache Hits: %llu", cmdStats.cacheHits);
        ImGui::Text("Cache Misses: %llu", cmdStats.cacheMisses);
        ImGui::Text("Errors: %llu", cmdStats.errorCount);
        ImGui::Text("Vulkan Context Stats:");
        ImGui::Text("Frame Count: %llu", vk_context.frameCount);
        ImGui::Text("Render Latency: %llu us", vk_context.renderLatencyUs);
        ImGui::Text("Cache Hits: %llu", vk_context.cacheHits);
        ImGui::Text("Cache Misses: %llu", vk_context.cacheMisses);
        ImGui::End();
      }

      if (show_font_debug_window) {
        ImGui::Begin("Font Debug", &show_font_debug_window);

        ImGui::Text("Font Metrics and Atlas Information");
        ImGui::Separator();

        ImGuiIO &io = ImGui::GetIO();

        // Display information
        ImGui::Text("Display Scale: %.2f", io.DisplayFramebufferScale.x);
        ImGui::Text("Font Global Scale: %.2f", io.FontGlobalScale);

        // Current font information
        if (io.FontDefault) {
          ImGui::Text("Default Font Information:");
          ImGui::Indent();
          ImGui::Text("Font Size: %.1f", io.FontDefault->FontSize);
          ImGui::Text("Scale: %.2f", io.FontDefault->Scale);
          ImGui::Text("Ascent: %.1f", io.FontDefault->Ascent);
          ImGui::Text("Descent: %.1f", io.FontDefault->Descent);
          ImGui::Text("Height: %.1f (Ascent - Descent: %.1f)",
                      io.FontDefault->Ascent - io.FontDefault->Descent,
                      io.FontDefault->Ascent + (-io.FontDefault->Descent));
          ImGui::Text("Fallback Advance X: %.1f",
                      io.FontDefault->FallbackAdvanceX);
          ImGui::Text("Fallback Character: U+%04X",
                      io.FontDefault->FallbackChar);
          ImGui::Text("Ellipsis Character: U+%04X",
                      io.FontDefault->EllipsisChar);
          ImGui::Text("Container Atlas: %p", io.FontDefault->ContainerAtlas);
          ImGui::Text("Sources Count: %d", io.FontDefault->SourcesCount);
          ImGui::Unindent();
        }

        ImGui::Separator();

        // Font atlas information
        if (io.Fonts) {
          ImGui::Text("Font Atlas Information:");
          ImGui::Indent();
          ImGui::Text("Atlas Size: %dx%d", io.Fonts->TexWidth,
                      io.Fonts->TexHeight);
          ImGui::Text("Font Count: %d", io.Fonts->Fonts.Size);
          ImGui::Text("Flags: 0x%08X", io.Fonts->Flags);
          ImGui::Text("Texture ID: %llu", (unsigned long long)io.Fonts->TexID);
          ImGui::Text("Texture Desired Width: %d", io.Fonts->TexDesiredWidth);
          ImGui::Text("Texture Glyph Padding: %d", io.Fonts->TexGlyphPadding);
          ImGui::Text("Locked: %s", io.Fonts->Locked ? "Yes" : "No");
          ImGui::Unindent();

          ImGui::Separator();

          // List all loaded fonts
          ImGui::Text("Loaded Fonts:");
          for (int i = 0; i < io.Fonts->Fonts.Size; i++) {
            ImFont *font = io.Fonts->Fonts[i];
            ImGui::PushID(i);

            if (ImGui::TreeNode("Font", "Font %d: %.1fpx", i, font->FontSize)) {
              ImGui::Text("Font Size: %.1f", font->FontSize);
              ImGui::Text("Scale: %.2f", font->Scale);
              ImGui::Text("Ascent: %.1f", font->Ascent);
              ImGui::Text("Descent: %.1f", font->Descent);
              ImGui::Text("Glyphs Count: %d", font->Glyphs.Size);
              ImGui::Text("Index Advance X Count: %d",
                          font->IndexAdvanceX.Size);
              ImGui::Text("Index Lookup Count: %d", font->IndexLookup.Size);
              ImGui::Text("Sources Count: %d", font->SourcesCount);
              ImGui::Text("Metrics Total Surface: %d",
                          font->MetricsTotalSurface);

              // Source font configurations
              if (font->Sources && font->SourcesCount > 0) {
                ImGui::Text("Font Sources:");
                ImGui::Indent();
                for (int j = 0; j < font->SourcesCount; j++) {
                  const ImFontConfig *cfg = &font->Sources[j];
                  ImGui::Text("Source %d:", j);
                  ImGui::Indent();
                  ImGui::Text("Size Pixels: %.1f", cfg->SizePixels);
                  ImGui::Text("Oversample H: %d, V: %d", cfg->OversampleH,
                              cfg->OversampleV);
                  ImGui::Text("Pixel Snap H: %s",
                              cfg->PixelSnapH ? "Yes" : "No");
                  ImGui::Text("Glyph Offset: %.1f, %.1f", cfg->GlyphOffset.x,
                              cfg->GlyphOffset.y);
                  ImGui::Text("Glyph Min/Max Advance X: %.1f, %.1f",
                              cfg->GlyphMinAdvanceX, cfg->GlyphMaxAdvanceX);
                  ImGui::Text("Merge Mode: %s", cfg->MergeMode ? "Yes" : "No");
                  ImGui::Text("Font Builder Flags: 0x%08X",
                              cfg->FontBuilderFlags);
                  ImGui::Text("Rasterizer Multiply: %.2f",
                              cfg->RasterizerMultiply);
                  ImGui::Text("Rasterizer Density: %.2f",
                              cfg->RasterizerDensity);
                  if (cfg->Name[0]) {
                    ImGui::Text("Name: %s", cfg->Name);
                  }
                  ImGui::Unindent();
                }
                ImGui::Unindent();
              }

              ImGui::Separator();

              // Show font sample text
              ImGui::Text("Font Sample:");
              ImGui::Indent();
              ImGui::PushFont(font);
              ImGui::Text("The quick brown fox jumps over the lazy dog");
              ImGui::Text("ABCDEFGHIJKLMNOPQRSTUVWXYZ");
              ImGui::Text("abcdefghijklmnopqrstuvwxyz");
              ImGui::Text("0123456789 !@#$%%^&*()");
              ImGui::PopFont();
              ImGui::Unindent();

              ImGui::TreePop();
            }
            ImGui::PopID();
          }

          ImGui::Separator();

          // Show font atlas texture
          ImGui::Text("Font Atlas Texture:");
          if (io.Fonts->TexID) {
            static float atlas_scale = 1.0f;
            ImGui::SliderFloat("Atlas Scale", &atlas_scale, 0.1f, 3.0f, "%.1f");

            float tex_w = (float)io.Fonts->TexWidth;
            float tex_h = (float)io.Fonts->TexHeight;
            ImVec2 display_size =
                ImVec2(tex_w * atlas_scale, tex_h * atlas_scale);

            if (display_size.x > 0 && display_size.y > 0) {
              ImGui::Image(io.Fonts->TexID, display_size);
            }
          } else {
            ImGui::Text("Atlas texture not available");
          }
        }

        ImGui::End();
      }

      if (show_input_window) {
        ImGui::Begin("Input", &show_input_window);
        auto &controllerManager = emulator->GetControllerManager();
        for (int i = 0; i < controllerManager.GetControllerCount(); ++i) {
          ImGui::Text("Controller %d:", i);
          ImGui::Text(
              "Buttons: A=%d, B=%d, X=%d, Y=%d",
              controllerManager.GetButtonState(i, SDL_CONTROLLER_BUTTON_A),
              controllerManager.GetButtonState(i, SDL_CONTROLLER_BUTTON_B),
              controllerManager.GetButtonState(i, SDL_CONTROLLER_BUTTON_X),
              controllerManager.GetButtonState(i, SDL_CONTROLLER_BUTTON_Y));
          ImGui::Text(
              "Left Stick: X=%.2f, Y=%.2f",
              controllerManager.GetAxisState(i, SDL_CONTROLLER_AXIS_LEFTX) /
                  32768.0f,
              controllerManager.GetAxisState(i, SDL_CONTROLLER_AXIS_LEFTY) /
                  32768.0f);
        }
        if (inputManager) {
          auto inputStats = inputManager->GetStats();
          ImGui::Text("Input Stats: Events=%llu, Latency=%llu us",
                      inputStats.eventCount, inputStats.totalLatencyUs);
        }
        ImGui::End();
      }

      // PKG Installation Window
      if (show_install_pkg_window) {
        ImGui::Begin("Install PKG File", &show_install_pkg_window);

        static char pkgPathBuf[260] = "";
        static char installPathBuf[260] = "/mnt/sandbox/pfsmnt";
        static std::string statusMessage = "";
        static bool isInstalling = false;
        static float installProgress = 0.0f;
        static std::future<bool> installFuture;
        static std::string pkgContentId, pkgVersion, pkgTitle;
        static bool pkgInfoLoaded = false;

        ImGui::Text("Select PKG file to install:");
        ImGui::InputText("PKG Path", pkgPathBuf, sizeof(pkgPathBuf));
        ImGui::SameLine();
        if (ImGui::Button("Browse PKG...")) {
// Simple file browser using Windows API on Windows
#ifdef _WIN32
          OPENFILENAMEA ofn;
          ZeroMemory(&ofn, sizeof(ofn));
          ofn.lStructSize = sizeof(ofn);
          ofn.lpstrFile = pkgPathBuf;
          ofn.nMaxFile = sizeof(pkgPathBuf);
          ofn.lpstrFilter = "PKG Files\0*.pkg\0All Files\0*.*\0";
          ofn.nFilterIndex = 1;
          ofn.lpstrFileTitle = NULL;
          ofn.nMaxFileTitle = 0;
          ofn.lpstrInitialDir = NULL;
          ofn.Flags = OFN_PATHMUSTEXIST | OFN_FILEMUSTEXIST;

          if (GetOpenFileNameA(&ofn)) {
            pkgInfoLoaded = false;
            statusMessage = "";
          }
#endif
        }

        // Load PKG info when path changes
        if (strlen(pkgPathBuf) > 0 && !pkgInfoLoaded && !isInstalling) {
          try {
            if (emulator &&
                emulator->GetPKGInstaller().GetPKGInfo(pkgPathBuf, pkgContentId,
                                                       pkgVersion, pkgTitle)) {
              pkgInfoLoaded = true;
              statusMessage = "PKG information loaded successfully";
            } else {
              statusMessage =
                  "Failed to load PKG information - file may be invalid";
            }
          } catch (const std::exception &e) {
            statusMessage = fmt::format("Error loading PKG info: {}", e.what());
          }
        }

        if (pkgInfoLoaded) {
          ImGui::Separator();
          ImGui::Text("PKG Information:");
          ImGui::Text("Title: %s", pkgTitle.c_str());
          ImGui::Text("Content ID: %s", pkgContentId.c_str());
          ImGui::Text("Version: %s", pkgVersion.c_str());
          ImGui::Separator();
        }

        ImGui::Text("Installation path:");
        ImGui::InputText("Install Path", installPathBuf,
                         sizeof(installPathBuf));

        if (!statusMessage.empty()) {
          ImGui::Separator();
          ImGui::TextWrapped("Status: %s", statusMessage.c_str());
        }

        if (isInstalling) {
          ImGui::Separator();
          ImGui::Text("Installing...");
          ImGui::ProgressBar(installProgress, ImVec2(-1.0f, 0.0f));

          // Check if installation is complete
          if (installFuture.valid() &&
              installFuture.wait_for(std::chrono::milliseconds(0)) ==
                  std::future_status::ready) {
            try {
              bool success = installFuture.get();
              if (success) {
                statusMessage = "PKG installed successfully!";
                spdlog::info("PKG {} installed successfully to {}", pkgPathBuf,
                             installPathBuf);
              } else {
                statusMessage = "PKG installation failed!";
                spdlog::error("Failed to install PKG {}", pkgPathBuf);
              }
            } catch (const std::exception &e) {
              statusMessage = fmt::format("Installation error: {}", e.what());
              spdlog::error("PKG installation exception: {}", e.what());
            }
            isInstalling = false;
            installProgress = 0.0f;
          } else {
            // Update progress (simplified - would need actual progress tracking
            // in PKGInstaller)
            installProgress = std::min(installProgress + 0.01f, 0.99f);
          }
        } else {
          ImGui::Separator();
          if (ImGui::Button("Install PKG") && strlen(pkgPathBuf) > 0) {
            if (emulator) {
              statusMessage = "Starting installation...";
              isInstalling = true;
              installProgress = 0.0f;

              installFuture = std::async(
                  std::launch::async,
                  [emulatorPtr = emulator.get(),
                   pkgPath = std::string(pkgPathBuf),
                   installPath = std::string(installPathBuf)]() {
                    try {
                      return emulatorPtr->GetPKGInstaller().InstallPKG(
                          pkgPath, installPath);
                    } catch (const std::exception &e) {
                      spdlog::error("PKG installation failed: {}", e.what());
                      return false;
                    }
                  });
            } else {
              statusMessage = "Emulator not initialized";
            }
          }

          ImGui::SameLine();
          if (ImGui::Button("Validate PKG") && strlen(pkgPathBuf) > 0) {
            if (emulator) {
              try {
                bool isValid =
                    emulator->GetPKGInstaller().ValidatePKG(pkgPathBuf);
                statusMessage = isValid ? "PKG file is valid"
                                        : "PKG file is invalid or corrupted";
              } catch (const std::exception &e) {
                statusMessage = fmt::format("Validation error: {}", e.what());
              }
            }
          }
        }

        ImGui::Separator();
        if (ImGui::Button("Close"))
          show_install_pkg_window = false;

        ImGui::End();
      }

      // Set Game Directory Window
      if (show_set_game_directory_window) {
        ImGui::Begin("Set Game Directory", &show_set_game_directory_window);

        static char gameDirectoryBuf[260] = "";
        static std::string statusMessage = "";
        static std::vector<std::string> installedPackages;
        static bool packagesLoaded = false;

        // Load installed packages list asynchronously
        static std::future<std::vector<std::string>> packagesFuture;
        static bool packagesLoading = false;

        if (!packagesLoaded && !packagesLoading && emulator) {
          packagesLoading = true;
          statusMessage = "Loading installed packages...";
          spdlog::info("Starting async load of installed packages list...");

          // Launch async task to load packages
          packagesFuture = std::async(std::launch::async, [&emulator]() {
            try {
              return emulator->GetPKGInstaller().ListInstalledPackages();
            } catch (const std::exception &e) {
              spdlog::error("Failed to load installed packages: {}", e.what());
              return std::vector<std::string>{};
            }
          });
        }

        // Check if async loading is complete
        if (packagesLoading && packagesFuture.valid()) {
          if (packagesFuture.wait_for(std::chrono::milliseconds(0)) == std::future_status::ready) {
            try {
              installedPackages = packagesFuture.get();
              packagesLoaded = true;
              packagesLoading = false;

              if (installedPackages.empty()) {
                statusMessage = "No installed packages found";
              } else {
                statusMessage = fmt::format("Found {} installed package(s)",
                                            installedPackages.size());
              }
              spdlog::info("Installed packages loaded successfully");
            } catch (const std::exception &e) {
              statusMessage = fmt::format("Error loading packages: {}", e.what());
              spdlog::error("Failed to load installed packages: {}", e.what());
              packagesLoaded = true;
              packagesLoading = false;
            }
          }
        }

        ImGui::Text("Current game directory:");
        if (emulator) {
          std::string currentDir = emulator->GetFilesystem().GetGameDirectory();
          ImGui::TextWrapped("%s", currentDir.c_str());
        }

        ImGui::Separator();
        ImGui::Text("Select new game directory:");
        ImGui::InputText("Directory Path", gameDirectoryBuf,
                         sizeof(gameDirectoryBuf));
        ImGui::SameLine();
        if (ImGui::Button("Browse...")) {
#ifdef _WIN32
          BROWSEINFOA bi = {0};
          bi.lpszTitle = "Select Game Directory";
          bi.ulFlags = BIF_RETURNONLYFSDIRS | BIF_NEWDIALOGSTYLE;

          LPITEMIDLIST pidl = SHBrowseForFolderA(&bi);
          if (pidl != 0) {
            SHGetPathFromIDListA(pidl, gameDirectoryBuf);
            IMalloc *imalloc = 0;
            if (SUCCEEDED(SHGetMalloc(&imalloc))) {
              imalloc->Free(pidl);
              imalloc->Release();
            }
          }
#endif
        }

        if (ImGui::Button("Set Directory") && strlen(gameDirectoryBuf) > 0) {
          if (emulator) {
            try {
              spdlog::info("Setting game directory to: {}", gameDirectoryBuf);

              // Check if directory exists and is accessible
              if (!std::filesystem::exists(gameDirectoryBuf)) {
                statusMessage = "Error: Directory does not exist";
                spdlog::error("Game directory does not exist: {}", gameDirectoryBuf);
              } else if (!std::filesystem::is_directory(gameDirectoryBuf)) {
                statusMessage = "Error: Path is not a directory";
                spdlog::error("Game directory path is not a directory: {}", gameDirectoryBuf);
              } else {
                emulator->GetFilesystem().SetGameDirectory(gameDirectoryBuf);
                statusMessage = "Game directory updated successfully";
                spdlog::info("Game directory set successfully");

                // Save to settings
                spdlog::info("Saving settings...");
                std::unique_lock<std::shared_mutex> lock(settingsMutex);
                settings.game_directory = gameDirectoryBuf;
                SaveSettings();
                spdlog::info("Settings saved");

                // Reset packages loaded flag to trigger reload
                packagesLoaded = false;
                packagesLoading = false;
                spdlog::info("Reset packages loaded flag");

                // Clear game list to force refresh when user manually refreshes
                game_list.clear();
                spdlog::info("Cleared game list - user must manually refresh");
              }
            } catch (const std::exception &e) {
              statusMessage =
                  fmt::format("Error setting directory: {}", e.what());
              spdlog::error("Failed to set game directory: {}", e.what());
            }
          }
        }

        if (!statusMessage.empty()) {
          ImGui::Separator();
          ImGui::TextWrapped("Status: %s", statusMessage.c_str());
        }

        // Show installed packages
        if (!installedPackages.empty()) {
          ImGui::Separator();
          ImGui::Text("Installed Packages:");

          static int selectedPackage = -1;
          for (int i = 0; i < installedPackages.size(); ++i) {
            if (ImGui::Selectable(installedPackages[i].c_str(),
                                  selectedPackage == i)) {
              selectedPackage = i;
            }
          }

          if (selectedPackage >= 0 &&
              selectedPackage < installedPackages.size()) {
            ImGui::Separator();
            ImGui::Text("Selected: %s",
                        installedPackages[selectedPackage].c_str());

            if (ImGui::Button("Uninstall Selected")) {
              if (emulator) {
                try {
                  bool success = emulator->GetPKGInstaller().UninstallPackage(
                      installedPackages[selectedPackage]);
                  if (success) {
                    statusMessage = "Package uninstalled successfully";
                    packagesLoaded = false; // Force refresh
                    packagesLoading = false;
                    selectedPackage = -1;
                  } else {
                    statusMessage = "Failed to uninstall package";
                  }
                } catch (const std::exception &e) {
                  statusMessage = fmt::format("Uninstall error: {}", e.what());
                }
              }
            }
          }
        }

        ImGui::Separator();
        if (ImGui::Button("Refresh Packages")) {
          packagesLoaded = false;
          packagesLoading = false;
          statusMessage = "Refreshing package list...";
        }

        ImGui::SameLine();
        if (ImGui::Button("Close"))
          show_set_game_directory_window = false;

        ImGui::End();
      }

      // Display Settings Window
      if (show_display_settings) {
        ImGui::Begin("Display Settings", &show_display_settings);

        ImGui::Text("Window Settings");
        ImGui::Separator();

        ImGui::SliderInt("Window Width", &settings.width, 640, 3840);
        ImGui::SliderInt("Window Height", &settings.height, 480, 2160);
        ImGui::Checkbox("Fullscreen", &settings.fullscreen);
        ImGui::Checkbox("VSync", &settings.vsync);
        ImGui::SliderInt("FPS Limit", &settings.framerate_limit, 30, 240);
        ImGui::SliderFloat("UI Scale", &settings.ui_scale, 0.5f, 3.0f);

        ImGui::Spacing();
        ImGui::Text("Performance Overlay");
        ImGui::Separator();

        ImGui::Checkbox("Show FPS", &settings.show_fps);
        ImGui::Checkbox("Show Performance Metrics", &settings.show_performance);

        ImGui::Spacing();
        if (ImGui::Button("Apply Window Settings")) {
          SDL_SetWindowSize(window.get(), settings.width, settings.height);
          SDL_SetWindowFullscreen(
              window.get(),
              settings.fullscreen ? SDL_WINDOW_FULLSCREEN_DESKTOP : 0);
        }
        ImGui::SameLine();
        if (ImGui::Button("Save Settings")) {
          SaveSettings();
        }

        ImGui::End();
      }

      // Audio Settings Window
      if (show_audio_settings) {
        ImGui::Begin("Audio Settings", &show_audio_settings);

        ImGui::Text("Audio Configuration");
        ImGui::Separator();

        ImGui::Checkbox("Audio Enabled", &settings.audio_enabled);
        ImGui::SliderFloat("Master Volume", &settings.master_volume, 0.0f,
                           1.0f);
        ImGui::SliderFloat("SFX Volume", &settings.sfx_volume, 0.0f, 1.0f);
        ImGui::SliderFloat("Music Volume", &settings.music_volume, 0.0f, 1.0f);
        ImGui::SliderInt("Audio Latency (ms)", &settings.audio_latency, 10,
                         200);

        ImGui::Spacing();
        ImGui::Text("Audio Device: %s", settings.audio_device.c_str());

        ImGui::Spacing();
        if (ImGui::Button("Apply Audio Settings")) {
          emulator->GetAudio().SetEnabled(settings.audio_enabled);
          // Apply other audio settings here
        }
        ImGui::SameLine();
        if (ImGui::Button("Save Settings")) {
          SaveSettings();
        }

        ImGui::End();
      }

      // CPU Settings Window
      if (show_cpu_settings) {
        ImGui::Begin("CPU Settings", &show_cpu_settings);

        ImGui::Text("CPU Configuration");
        ImGui::Separator();

        ImGui::SliderInt("CPU Thread Count", &settings.cpu_thread_count, 1, 16);
        ImGui::Checkbox("JIT Compilation", &settings.jit_enabled);
        ImGui::Checkbox("SIMD Optimizations", &settings.simd_optimizations);
        ImGui::Checkbox("Branch Prediction", &settings.branch_prediction);
        ImGui::SliderInt("Cache Size (MB)", &settings.cache_size_mb, 64, 1024);

        ImGui::Spacing();
        ImGui::Text("Performance");
        ImGui::Separator();

        ImGui::SliderFloat("Emulation Speed", &settings.emulation_speed, 0.1f,
                           3.0f);
        ImGui::Checkbox("Pause on Focus Loss", &settings.pause_on_focus_loss);

        ImGui::Spacing();
        if (ImGui::Button("Apply CPU Settings")) {
          // Apply CPU settings to emulator
          spdlog::info("CPU settings applied");
        }
        ImGui::SameLine();
        if (ImGui::Button("Save Settings")) {
          SaveSettings();
        }

        ImGui::End();
      }

      // GPU Settings Window
      if (show_gpu_settings) {
        ImGui::Begin("GPU Settings", &show_gpu_settings);

        if (ImGui::BeginTabBar("GPUTabs")) {
          if (ImGui::BeginTabItem("Configuration")) {
            ImGui::Text("Graphics Configuration");
            ImGui::Separator();

            const char *backends[] = {"Vulkan", "OpenGL"};
            int current_backend = (settings.gpu_backend == "Vulkan") ? 0 : 1;
            if (ImGui::Combo("GPU Backend", &current_backend, backends, 2)) {
              settings.gpu_backend = backends[current_backend];
            }

            ImGui::SliderInt("Resolution Scale", &settings.resolution_scale, 1,
                             4);
            ImGui::Checkbox("Anisotropic Filtering",
                            &settings.anisotropic_filtering);
            if (settings.anisotropic_filtering) {
              ImGui::SliderInt("Anisotropy Level", &settings.anisotropy_level,
                               1, 16);
            }

            ImGui::Checkbox("Anti-Aliasing", &settings.anti_aliasing);
            if (settings.anti_aliasing) {
              const char *aa_methods[] = {"FXAA", "MSAA 2x", "MSAA 4x",
                                          "MSAA 8x"};
              int current_aa = 0;
              if (settings.aa_method == "MSAA 2x")
                current_aa = 1;
              else if (settings.aa_method == "MSAA 4x")
                current_aa = 2;
              else if (settings.aa_method == "MSAA 8x")
                current_aa = 3;

              if (ImGui::Combo("AA Method", &current_aa, aa_methods, 4)) {
                settings.aa_method = aa_methods[current_aa];
              }
            }

            ImGui::Spacing();
            ImGui::Text("Shader Settings");
            ImGui::Separator();

            ImGui::Checkbox("Shader Cache", &settings.shader_cache);
            ImGui::Checkbox("Async Shader Compilation",
                            &settings.async_shaders);

            ImGui::Spacing();
            if (ImGui::Button("Apply GPU Settings")) {
              spdlog::info("GPU settings applied");
            }
            ImGui::SameLine();
            if (ImGui::Button("Save Settings")) {
              SaveSettings();
            }
            ImGui::EndTabItem();
          }

          if (ImGui::BeginTabItem("Statistics")) {
            ImGui::Text("GPU Statistics");
            ImGui::Separator();

            // ARCHITECTURAL FIX: Route GPU access through emulator methods
            // instead of direct GPU access This prevents state duplication and
            // ensures single source of truth
            auto gpuStats = emulator->GetGPU().GetStats();
            // RACE CONDITION FIX: Use .load() for atomic variables
            ImGui::Text("Shader Count: %llu", gpuStats.shaderCount.load());
            ImGui::Text("Draw Count: %llu", gpuStats.drawCount.load());
            ImGui::Text("Cache Hits: %llu", gpuStats.cacheHits.load());
            ImGui::Text("Cache Misses: %llu", gpuStats.cacheMisses.load());
            ImGui::Text("Total Latency: %llu us",
                        gpuStats.totalLatencyUs.load());
            ImGui::Text("Errors: %llu", gpuStats.errorCount.load());

            ImGui::Spacing();
            ImGui::Text("GPU Actions");
            ImGui::Separator();

            if (ImGui::Button("Clear Shader Cache")) {
              emulator->GetGPU().ClearShaderCache();
            }
            ImGui::SameLine();
            if (ImGui::Button("Clear Render Target Cache")) {
              emulator->GetGPU().ClearRenderTargetCache();
            }
            ImGui::EndTabItem();
          }

          ImGui::EndTabBar();
        }

        ImGui::End();
      }

      if (show_filesystem_settings) {
        ImGui::Begin("Filesystem Settings", &show_filesystem_settings);
        auto &fs = emulator->GetFilesystem();
        auto fsSettings = fs.GetSettings();
        static char mountPointBuf[128];
        std::strncpy(mountPointBuf, fsSettings.defaultMountPoint.c_str(),
                     sizeof(mountPointBuf) - 1);
        mountPointBuf[sizeof(mountPointBuf) - 1] =
            '\0'; // Ensure null termination
        ImGui::InputText("Default Mount Point", mountPointBuf,
                         sizeof(mountPointBuf));
        fsSettings.defaultMountPoint = mountPointBuf;
        ImGui::InputInt("Default File Mode", &fsSettings.defaultFileMode);
        ImGui::InputInt("Default Dir Mode", &fsSettings.defaultDirMode);
        ImGui::Checkbox("Case Sensitive", &fsSettings.enableCaseSensitivity);
        static char mountBuf[256] = "";
        ImGui::InputText("Add Mount", mountBuf, sizeof(mountBuf));
        if (ImGui::Button("Add Mount") && strlen(mountBuf) > 0) {
          try {
            std::filesystem::path mountPath(mountBuf);
            if (std::filesystem::exists(mountPath)) {
              fsSettings.additionalMounts.push_back(mountBuf);
              mountBuf[0] = '\0';
            } else {
              spdlog::error("Invalid mount path: {}", mountBuf);
            }
          } catch (const std::exception &e) {
            spdlog::error("Invalid mount path {}: {}", mountBuf, e.what());
          }
        }
        for (size_t i = 0; i < fsSettings.additionalMounts.size(); ++i) {
          ImGui::Text("Mount %zu: %s", i,
                      fsSettings.additionalMounts[i].c_str());
        }
        if (ImGui::Button("Save FS Settings")) {
          fs.SaveSettings("ps4_filesystem_settings.ini");
        }
        ImGui::SameLine();
        if (ImGui::Button("Load FS Settings")) {
          fs.LoadSettings("ps4_filesystem_settings.ini");
        }
        if (ImGui::Button("Apply Settings")) {
          fs.SetSettings(fsSettings);
        }
        ImGui::End();
      }

      // Display final status window if no other windows are open
      if (!show_demo_window && !show_another_window && !show_load_game_window &&
          !show_save_state_window && !show_load_state_window &&
          !show_filesystem_window && !show_diagnostics_window &&
          !show_input_window && !show_font_debug_window &&
          !show_display_settings && !show_audio_settings &&
          !show_cpu_settings && !show_gpu_settings && !show_memory_settings &&
          !show_filesystem_settings && !show_debug_settings &&
          !show_input_settings && !show_network_settings &&
          !show_compatibility_settings && !show_advanced_settings &&
          !show_game_browser && !show_performance_overlay) {
        ImGui::Begin("Emulator Status", nullptr,
                     ImGuiWindowFlags_AlwaysAutoResize |
                         ImGuiWindowFlags_NoCollapse);
        ImGui::Text(
            "PS4 Emulator is running.\nNo game loaded.\nIf you see only this "
            "window, the emulator is functional but not running a game.");
        ImGui::End();
      }

      ImGui::Render();
      ImDrawData *draw_data = ImGui::GetDrawData();
      bool skipFrame =
          draw_data->DisplaySize.x <= 0.0f || draw_data->DisplaySize.y <= 0.0f;
      uint32_t image_index = 0;
      if (!skipFrame) {
        renderFrame(vk_context, render_command_buffer, draw_data, image_index,
                    swapchain_rebuild, quit, *emulator);
      }

      auto now = std::chrono::steady_clock::now();
      auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
          now - lastFrame);
      if (elapsed < frameTime)
        std::this_thread::sleep_for(frameTime - elapsed);
      lastFrame = now;
    }

    emulator->Shutdown();
    ImGui_ImplVulkan_Shutdown();
    ImGui_ImplSDL2_Shutdown();
    ImGui::DestroyContext();
    cleanupVulkan(vk_context);
    SDL_Quit();
    return 0;
  } catch (const std::exception &e) {
    spdlog::error("Unhandled exception in main: {}", e.what());
    return EXIT_FAILURE;
  }
  return EXIT_SUCCESS;
}
} // namespace ps4

// SDL expects an entry point called SDL_main on Windows
int SDL_main(int argc, char *argv[]) { return ps4::main(); }

// For systems that don't use SDL's main handling
#ifndef SDL_MAIN_HANDLED
int main(int argc, char *argv[]) { return ps4::main(); }
#endif