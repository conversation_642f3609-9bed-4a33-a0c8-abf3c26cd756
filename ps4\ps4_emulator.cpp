#include "ps4_emulator.h"
#include "../cpu/cpu_diagnostics.h"
#include "../cpu/register.h"
#include "../cpu/x86_64_cpu.h"
#include "../emulator/interrupt_handler.h"
#include "../emulator/io_manager.h"
#include "../jit/jit_diagnostics.h"
#include "../jit/x86_64_jit_compiler.h"
#include "../loader/pkg_installer.h"
#include "../memory/memory.h"
#include "../memory/memory_diagnostics.h"
#include "../memory/ps4_mmu.h"
#include "../memory/tlb.h"
#include "../syscall/syscall_handler.h"
#include "../video_core/command_processor.h"
#include "../video_core/gnm_shader_translator.h"
#include "../video_core/tile_manager.h"
#include "fiber_manager.h"
#include "orbis_os.h"
#include "ps4_audio.h"
#include "ps4_controllers.h"
#include "ps4_filesystem.h"
#include "ps4_gpu.h"
#include "ps4_tsc.h"
#include "trophy_manager.h"
#include "zlib_wrapper.h"
#include <SDL2/SDL.h>
#include <algorithm>
#include <chrono>
#include <filesystem>
#include <fstream>
#include <spdlog/fmt/fmt.h>
#include <spdlog/spdlog.h>
#include <stdexcept>

namespace ps4 {
// Define singleton storage
PS4Emulator *PS4Emulator::s_instance = nullptr;

/**
 * @brief Constructs the emulator.
 */
PS4Emulator::PS4Emulator()
    : m_running(false), m_paused(false) {
  auto start = std::chrono::steady_clock::now();
  s_instance = this;
  m_stats = Stats();
  spdlog::info("PS4Emulator constructed");
  auto end = std::chrono::steady_clock::now();
  auto latency =
      std::chrono::duration_cast<std::chrono::microseconds>(end - start)
          .count();
  m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
}

/**
 * @brief Destructor, cleaning up resources.
 */
PS4Emulator::~PS4Emulator() {
  auto start = std::chrono::steady_clock::now();
  Shutdown();
  s_instance = nullptr;
  spdlog::info("PS4Emulator destroyed");
  auto end = std::chrono::steady_clock::now();
  auto latency =
      std::chrono::duration_cast<std::chrono::microseconds>(end - start)
          .count();
  m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
}

/**
 * @brief Singleton accessor.
 * @return Reference to the emulator instance.
 */
PS4Emulator &PS4Emulator::GetInstance() {
  if (!s_instance) {
    throw PS4EmulatorException("No emulator instance exists");
  }
  return *s_instance;
}

/**
 * @brief Initializes the emulator.
 * @param window SDL window.
 * @param vulkanContext Shared Vulkan context.
 * @return True on success, false otherwise.
 */
bool PS4Emulator::Initialize(SDL_Window *window, VulkanContext *vulkanContext) {
  auto start = std::chrono::steady_clock::now();

  // CRITICAL FIX: Create components with minimal lock scope to prevent
  // deadlocks
  {
    std::unique_lock<std::shared_mutex> lock(m_emulatorMutex);
    m_window = window;
    m_mmu = std::make_unique<PS4MMU>();
    m_tlb = std::make_unique<ps4::TLB>();

    m_os = std::make_unique<OrbisOS>(*this);
    m_filesystem = std::make_unique<PS4Filesystem>(*this);
    m_syscallHandler = std::make_unique<SyscallHandler>(*this);
    m_controllerManager = std::make_unique<PS4ControllerManager>(*m_mmu);
    m_audio = std::make_unique<PS4Audio>(*m_mmu);
    m_tsc = std::make_unique<PS4TSC>();
    m_shaderTranslator = std::make_unique<GNMShaderTranslator>();
    // Initialize GPU without tileManager; will set tileManager later to resolve
    // circular dependency
    m_gpu = std::make_unique<PS4GPU>(*m_mmu, std::move(m_shaderTranslator),
                                     nullptr, vulkanContext, window);
    // Create command processor now that GPU exists
    m_commandProcessor = std::make_unique<CommandProcessor>(*m_mmu, *m_gpu);
    // Create TileManager with references to GPU's GNM state and the command
    // processor
    m_tileManager = std::make_unique<TileManager>(m_gpu->GetRegisterState(),
                                                  *m_commandProcessor);
    // Initialize TileManager before moving it to GPU
    if (!m_tileManager->Initialize()) {
      spdlog::error("TileManager initialization failed during construction");
      throw PS4EmulatorException("TileManager initialization failed");
    }
    // Inject tileManager into GPU
    m_gpu->SetTileManager(std::move(m_tileManager));
    m_fiberManager = std::make_unique<FiberManager>();
    m_trophyManager = std::make_unique<TrophyManager>();
    m_zlibWrapper = std::make_unique<ZlibWrapper>();
    m_pkgInstaller = std::make_unique<PKGInstaller>(m_filesystem.get(), this);
    for (uint32_t i = 0; i < 8; ++i) {
      auto cpu = std::make_unique<x86_64::X86_64CPU>(*this, *m_mmu, i);
      m_jitCompilers.emplace_back(
          std::make_unique<x86_64::X86_64JITCompiler>(cpu.get()));
      m_cpus.push_back(std::move(cpu));
    }
    // Create interrupt handler after CPUs are created
    m_interruptHandler =
        std::make_unique<x86_64::InterruptHandler>(*m_cpus[0], *m_mmu);
    m_ioManager =
        std::make_unique<x86_64::IOManager>(*this, *m_interruptHandler);
  } // Release emulator mutex before component initialization

  try {
    // Initialize components without holding emulator mutex to prevent deadlocks
    if (!m_mmu->Initialize()) {
      spdlog::error("MMU initialization failed");
      throw PS4EmulatorException("MMU initialization failed");
    }
    if (!m_tsc->Initialize()) {
      spdlog::error("TSC initialization failed");
      throw PS4EmulatorException("TSC initialization failed");
    }
    if (!m_filesystem->Initialize()) {
      spdlog::error("Filesystem initialization failed");
      throw PS4EmulatorException("Filesystem initialization failed");
    }
    if (!m_os->Initialize()) {
      spdlog::error("OS initialization failed");
      throw PS4EmulatorException("OS initialization failed");
    }
    if (!m_ioManager->InitializeStandardDevices()) {
      spdlog::error("IOManager initialization failed");
      throw PS4EmulatorException("IOManager initialization failed");
    }
    if (!m_fiberManager->Initialize()) {
      spdlog::error("FiberManager initialization failed");
      throw PS4EmulatorException("FiberManager initialization failed");
    }
    if (!m_trophyManager->Initialize("default_user")) {
      spdlog::error("TrophyManager initialization failed");
      throw PS4EmulatorException("TrophyManager initialization failed");
    }
    if (!m_gpu->Initialize()) {
      spdlog::error("GPU initialization failed");
      throw PS4EmulatorException("GPU initialization failed");
    }
    spdlog::info("About to initialize ControllerManager...");
    if (!m_controllerManager->Initialize()) {
      spdlog::error("ControllerManager initialization failed");
      throw PS4EmulatorException("ControllerManager initialization failed");
    }
    spdlog::info("ControllerManager initialized, about to initialize Audio...");
    if (!m_audio->Initialize()) {
      spdlog::error("Audio initialization failed");
      throw PS4EmulatorException("Audio initialization failed");
    }
    spdlog::info("Audio initialized successfully");
    spdlog::info("Initializing CPUs...");
    for (const auto &cpu : m_cpus) {
      if (!cpu->Initialize()) {
        spdlog::error("CPU initialization failed");
        throw PS4EmulatorException("CPU initialization failed");
      }
    }
    spdlog::info("CPU initialization completed, starting InterruptHandler...");
    m_interruptHandler->Initialize();
    spdlog::info("InterruptHandler initialization completed");

    // Update final state with proper locking and safe diagnostic reset
    {
      std::unique_lock<std::shared_mutex> lock(m_emulatorMutex);
      try {
        // FIX: Reset diagnostics safely with error handling
        spdlog::info("Resetting diagnostic metrics...");
        MemoryDiagnostics::GetInstance().ResetMetrics();
        spdlog::info("Memory diagnostics reset completed");

        x86_64::CPUDiagnostics::GetInstance().ResetMetrics();
        spdlog::info("CPU diagnostics reset completed");

        x86_64::JITDiagnostics::GetInstance().ResetMetrics();
        spdlog::info("JIT diagnostics reset completed");

        m_stats = Stats();
        m_running = false;
        m_paused = false;
        m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
        spdlog::info("Emulator state reset completed");
      } catch (const std::exception &e) {
        spdlog::error("Failed to reset diagnostics: {}", e.what());
        // Continue initialization even if diagnostics fail
        m_stats = Stats();
        m_running = false;
        m_paused = false;
        m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
      }
    }

    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
    spdlog::info("PS4Emulator initialized, latency={}us", latency);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("PS4Emulator initialization failed: {}", e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
    return false;
  }
}

/**
 * @brief Shuts down the emulator.
 */
void PS4Emulator::Shutdown() {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_emulatorMutex);
  try {
    Stop();
    m_zlibWrapper.reset();
    m_trophyManager.reset();
    m_fiberManager.reset();
    m_commandProcessor.reset();
    m_gpu.reset();
    m_tileManager.reset();
    m_shaderTranslator.reset();
    m_tsc.reset();
    m_audio.reset();
    m_controllerManager.reset();
    m_syscallHandler.reset();
    m_filesystem.reset();
    m_os.reset();
    m_ioManager.reset();
    m_interruptHandler.reset();
    m_jitCompilers.clear();
    m_cpus.clear();
    m_tlb.reset();
    m_mmu.reset();
    m_breakpoints.clear();
    m_loadedModules.clear();
    m_stats = Stats();
    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    lock.unlock();
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
    spdlog::info("PS4Emulator shutdown");
  } catch (const std::exception &e) {
    spdlog::error("PS4Emulator shutdown failed: {}", e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
  }
}

/**
 * @brief Loads a game executable or PKG file.
 * @param path Path to the executable or PKG file.
 * @return True on success, false otherwise.
 */
bool PS4Emulator::LoadGame(const std::string &path) {
  auto start = std::chrono::steady_clock::now();

  try {
    // Check file extension to determine file type
    std::filesystem::path filePath(path);
    std::string extension = filePath.extension().string();
    std::transform(extension.begin(), extension.end(), extension.begin(),
                   ::tolower);

    if (extension == ".pkg") {
      // Handle PKG file installation
      spdlog::info("LoadGame: Detected PKG file, installing: {}", path);

      if (!m_pkgInstaller) {
        spdlog::error("LoadGame: PKG installer not initialized");
        std::unique_lock<std::shared_mutex> lock(m_emulatorMutex);
        m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
        return false;
      }

      // Install the PKG file
      if (!m_pkgInstaller->InstallPKG(path)) {
        spdlog::error("LoadGame: Failed to install PKG: {}", path);
        std::unique_lock<std::shared_mutex> lock(m_emulatorMutex);
        m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
        return false;
      } // Get PKG info to find the main executable
      std::string contentId, version, title;
      if (!m_pkgInstaller->GetPKGInfo(path, contentId, version, title)) {
        spdlog::error("LoadGame: Failed to get PKG info: {}", path);
        std::unique_lock<std::shared_mutex> lock(m_emulatorMutex);
        m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
        return false;
      }

      // Try to find and load the main executable from the installed package
      std::string installPath =
          "/mnt/sandbox/pfsmnt/" + contentId + "/eboot.bin";

      spdlog::info("LoadGame: Looking for main executable at: {}", installPath);

      // Check if file exists using filesystem
      std::ifstream testFile(installPath);
      if (!testFile.good()) {
        spdlog::warn("LoadGame: Main executable not found at {}, trying fallback", installPath);
        // Fallback to alternative path
        installPath = "/app0/eboot.bin";
      }
      testFile.close();

      std::ifstream testFile2(installPath);
      if (!testFile2.good()) {
        spdlog::error(
            "LoadGame: Cannot find main executable after PKG installation at {} or fallback path", installPath);
        std::unique_lock<std::shared_mutex> lock(m_emulatorMutex);
        m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
        return false;
      }

      spdlog::info("LoadGame: Found main executable at: {}", installPath);

      // Load the main executable using ELF loader directly with file path
      LoadedElf elf;
      ElfLoader loader(*this);
      if (!loader.Load(installPath, elf, false)) {
        spdlog::error("LoadGame: Failed to load main executable from PKG: {}",
                      installPath);
        std::unique_lock<std::shared_mutex> lock(m_emulatorMutex);
        m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
        return false;
      }

      // Read the executable file data
      std::ifstream execFile(installPath, std::ios::binary);
      if (!execFile.good()) {
        spdlog::error("LoadGame: Failed to read executable file: {}",
                      installPath);
        std::unique_lock<std::shared_mutex> lock(m_emulatorMutex);
        m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
        return false;
      }

      execFile.seekg(0, std::ios::end);
      size_t fileSize = execFile.tellg();
      execFile.seekg(0, std::ios::beg);

      std::vector<uint8_t> fileData(fileSize);
      execFile.read(reinterpret_cast<char *>(fileData.data()), fileSize);
      execFile.close();

      // Create a temporary file for the ELF loader
      std::string tempPath =
          std::filesystem::temp_directory_path().string() + "/ps4_temp_" +
          std::to_string(std::hash<std::string>{}(installPath)) + ".bin";
      std::ofstream tempFile(tempPath, std::ios::binary);
      if (!tempFile.write(reinterpret_cast<const char *>(fileData.data()),
                          fileData.size())) {
        spdlog::error(
            "LoadGame: Failed to create temporary file for ELF loading");
        std::unique_lock<std::shared_mutex> lock(m_emulatorMutex);
        m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
        return false;
      }
      tempFile.close();

      if (!loader.Load(tempPath, elf, false)) {
        std::filesystem::remove(tempPath);
        spdlog::error("LoadGame: Failed to load main executable from PKG: {}",
                      installPath);
        std::unique_lock<std::shared_mutex> lock(m_emulatorMutex);
        m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
        return false;
      }

      // Clean up temporary file
      std::filesystem::remove(tempPath);

      // Update emulator state
      std::unique_lock<std::shared_mutex> lock(m_emulatorMutex);
      m_os->SceCreateProcess(installPath);
      m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
      lock.unlock();

      auto end = std::chrono::steady_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
      spdlog::info("LoadGame: PKG installed and loaded {} in {}us", path,
                   latency);
      return true;

    } else {
      // Handle regular ELF/BIN files
      spdlog::info("LoadGame: Loading ELF/BIN file: {}", path);

      // CRITICAL FIX: Load ELF without holding emulator mutex to prevent
      // deadlocks ElfLoader calls into MMU and OS which have their own mutexes
      LoadedElf elf;
      ElfLoader loader(*this);
      if (!loader.Load(path, elf, false)) {
        spdlog::error("LoadGame: Failed to load ELF: {}", path);
        std::unique_lock<std::shared_mutex> lock(m_emulatorMutex);
        m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
        return false;
      }

      // Only acquire emulator mutex for updating internal state
      std::unique_lock<std::shared_mutex> lock(m_emulatorMutex);
      m_os->SceCreateProcess(path);
      m_filesystem->MountDirectory(
          std::filesystem::path(path).parent_path().wstring());
      m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
      lock.unlock();

      auto end = std::chrono::steady_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
      spdlog::info("LoadGame: Loaded {} in {}us", path, latency);
      return true;
    }
  } catch (const std::exception &e) {
    spdlog::error("LoadGame: Failed to load {}: {}", path, e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
    return false;
  }
}

/**
 * @brief Starts the emulator execution.
 */
void PS4Emulator::Start() {
  auto start = std::chrono::steady_clock::now();

  // CRITICAL FIX: Minimize lock scope to prevent deadlocks with component
  // mutexes
  {
    std::unique_lock<std::shared_mutex> lock(m_emulatorMutex);
    if (m_running) {
      spdlog::warn("Start: Emulator already running");
      m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
      return;
    }
    m_running = true;
    m_paused = false;
    m_coreThreads.clear();
  }

  // Create core threads without holding emulator mutex to prevent deadlocks
  try {
    for (uint32_t i = 0; i < m_cpus.size(); i++) {
      m_coreThreads.emplace_back(&PS4Emulator::CoreThread, this, i);
    }
    m_fiberManager->SetSchedulingPolicy(SchedulingPolicy::PRIORITY);
    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
    spdlog::info("Emulator started with {} cores", m_cpus.size());
  } catch (const std::exception &e) {
    spdlog::error("Start emulator failed: {}", e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
  }
}

/**
 * @brief Pauses the emulator execution.
 */
void PS4Emulator::Pause() {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_emulatorMutex);
  try {
    if (!m_running) {
      spdlog::warn("Pause: Emulator not running");
      m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
      return;
    }
    m_paused = true;
    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
    spdlog::info("Emulator paused");
  } catch (const std::exception &e) {
    spdlog::error("Pause emulator failed: {}", e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
  }
}

/**
 * @brief Stops the emulator execution.
 */
void PS4Emulator::Stop() {
  auto start = std::chrono::steady_clock::now();

  // CRITICAL FIX: Prevent deadlock by not holding emulator mutex while joining
  // threads
  bool wasRunning = false;
  {
    std::unique_lock<std::shared_mutex> lock(m_emulatorMutex);
    if (!m_running) {
      spdlog::warn("Stop: Emulator not running");
      m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
      return;
    }
    wasRunning = true;
    m_running = false;
    m_paused = false;
  }

  // Join threads without holding emulator mutex to prevent deadlocks
  try {
    for (auto &thread : m_coreThreads) {
      if (thread.joinable()) {
        thread.join();
      }
    }

    // Clear threads with lock
    {
      std::unique_lock<std::shared_mutex> lock(m_emulatorMutex);
      m_coreThreads.clear();
    }
    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
    spdlog::info("Emulator stopped");
  } catch (const std::exception &e) {
    spdlog::error("Stop emulator failed: {}", e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
  }
}

/**
 * @brief Adds a breakpoint.
 * @param address Breakpoint address.
 * @param condition Optional condition.
 */
void PS4Emulator::AddBreakpoint(uint64_t address,
                                const std::string &condition) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_emulatorMutex);
  try {
    m_breakpoints.push_back({address, true, condition});
    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
    spdlog::info("Added breakpoint at 0x{:x}, condition='{}'", address,
                 condition);
  } catch (const std::exception &e) {
    spdlog::error("Add breakpoint failed: {}", e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
  }
}

/**
 * @brief Removes a breakpoint.
 * @param address Breakpoint address.
 */
void PS4Emulator::RemoveBreakpoint(uint64_t address) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_emulatorMutex);
  try {
    m_breakpoints.erase(std::remove_if(m_breakpoints.begin(),
                                       m_breakpoints.end(),
                                       [address](const Breakpoint &bp) {
                                         return bp.address == address;
                                       }),
                        m_breakpoints.end());
    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
    spdlog::info("Removed breakpoint at 0x{:x}", address);
  } catch (const std::exception &e) {
    spdlog::error("Remove breakpoint failed: {}", e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
  }
}

/**
 * @brief Adds a loaded module.
 * @param name Module name.
 * @param moduleId Module ID.
 */
void PS4Emulator::AddLoadedModule(const std::string &name, const int moduleId) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_emulatorMutex);
  try {
    m_loadedModules[name] = moduleId;
    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
    spdlog::info("Added loaded module: {} (ID {})", name, moduleId);
  } catch (const std::exception &e) {
    spdlog::error("Add loaded module failed: {}", e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
  }
}

/**
 * @brief Core execution thread for a CPU.
 * @param coreId CPU core index.
 */
void PS4Emulator::CoreThread(uint32_t coreId) {
  auto &cpu = *m_cpus[coreId];
  spdlog::info("CoreThread started for core {}", coreId);
  while (m_running) {
    auto start = std::chrono::steady_clock::now();
    try {
      if (m_paused) {
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
        continue;
      }

      // Check breakpoints with minimal locking
      bool shouldBreak = false;
      {
        std::shared_lock<std::shared_mutex> lock(m_emulatorMutex);
        uint64_t currentRIP = cpu.GetRegister(x86_64::Register::RIP);

        for (const auto &bp : m_breakpoints) {
          if (bp.enabled && currentRIP == bp.address) {
            bool conditionMet = true;
            // Evaluate bp.condition (CPU state parsing implementation)
            if (!bp.condition.empty()) {
              conditionMet =
                  this->EvaluateBreakpointCondition(bp.condition, cpu);
            }
            if (conditionMet) {
              shouldBreak = true;
              m_stats.breakpointHits.fetch_add(1, std::memory_order_relaxed);
              spdlog::info(
                  "Breakpoint hit at 0x{:x} on core {}, condition='{}' "
                  "evaluated to true",
                  bp.address, coreId, bp.condition);
              break;
            } else {
              spdlog::trace(
                  "Breakpoint at 0x{:x} condition='{}' evaluated to false",
                  bp.address, bp.condition);
            }
          }
        }
      } // Release lock before executing CPU cycle

      if (shouldBreak) {
        m_paused = true;
        continue;
      }

      if (!m_paused) {
        // Execute CPU cycle without holding emulator mutex to prevent deadlocks
        cpu.ExecuteCycle();

        // RACE CONDITION FIX: Use atomic operations for statistics
        m_stats.instructionsExecuted.fetch_add(1, std::memory_order_relaxed);
        m_stats.totalCycles.fetch_add(cpu.GetPipeline().GetStats().cycles,
                                      std::memory_order_relaxed);
        m_ioManager->Cycle();
        m_fiberManager->ScheduleNextFiber();
        m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
        MemoryDiagnostics::GetInstance().UpdateMetrics();
        x86_64::CPUDiagnostics::GetInstance().UpdateMetrics();
        x86_64::JITDiagnostics::GetInstance().UpdateMetrics();
      }
      auto end = std::chrono::steady_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);

      // Adaptive cycle timing: sleep if no activity
      if (m_stats.instructionsExecuted.load(std::memory_order_relaxed) == 0) {
        std::this_thread::sleep_for(std::chrono::microseconds(100));
      }
    } catch (const std::exception &e) {
      spdlog::error("CoreThread {} failed: {}", coreId, e.what());
      m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
    }
  }
  spdlog::info("CoreThread stopped for core {}", coreId);
}

/**
 * @brief Loads a module.
 * @param path Module path.
 * @param outElf Output LoadedElf structure.
 */
void PS4Emulator::LoadModule(const std::string &path, LoadedElf &outElf) {
  auto start = std::chrono::steady_clock::now();
  try {
    ElfLoader loader(*this);
    if (!loader.Load(path, outElf, true)) {
      spdlog::error("LoadModule: Failed to load module: {}", path);
      throw PS4EmulatorException("Failed to load module");
    }
    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
  } catch (const std::exception &e) {
    spdlog::error("LoadModule failed: {}", e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
    throw;
  }
}

/**
 * @brief Saves the emulator state.
 * @param path File path.
 */
void PS4Emulator::SaveState(const std::string &path) {
  auto start = std::chrono::steady_clock::now();

  // CRITICAL FIX: Minimize emulator mutex scope to prevent deadlocks with
  // component mutexes
  std::ofstream out;
  {
    std::unique_lock<std::shared_mutex> lock(m_emulatorMutex);
    out.open(path, std::ios::binary);
    if (!out) {
      spdlog::error("SaveState: Failed to open file: {}", path);
      throw PS4EmulatorException("Failed to save state");
    }
  }

  try {
    uint32_t version = 1;
    out.write(reinterpret_cast<const char *>(&version), sizeof(version));

    // Save component states without holding emulator mutex to prevent deadlocks
    for (auto &cpu : m_cpus) {
      cpu->SaveState(out);
    }
    m_mmu->SaveState(out);
    m_tlb->SaveState(out);
    for (auto &jit : m_jitCompilers) {
      jit->SaveState(out);
    }
    m_gpu->SaveState(out);
    m_tsc->SaveState(out);
    m_interruptHandler->SaveState(out);
    m_ioManager->SaveState(out);
    m_controllerManager->SaveState(out);
    m_audio->SaveState(out);
    m_os->SaveState(out);
    m_filesystem->SaveState(out);
    m_syscallHandler->SaveState(out);
    m_commandProcessor->SaveState(out);
    m_fiberManager->SaveState(out);
    m_trophyManager->SaveState(out);
    m_zlibWrapper->SaveState(out);
    uint64_t breakpointCount = m_breakpoints.size();
    out.write(reinterpret_cast<const char *>(&breakpointCount),
              sizeof(breakpointCount));
    for (const auto &bp : m_breakpoints) {
      out.write(reinterpret_cast<const char *>(&bp.address),
                sizeof(bp.address));
      out.write(reinterpret_cast<const char *>(&bp.enabled),
                sizeof(bp.enabled));
      uint32_t condLen = static_cast<uint32_t>(bp.condition.size());
      out.write(reinterpret_cast<const char *>(&condLen), sizeof(condLen));
      out.write(bp.condition.data(), condLen);
    }
    uint64_t moduleCount = m_loadedModules.size();
    out.write(reinterpret_cast<const char *>(&moduleCount),
              sizeof(moduleCount));
    for (const auto &[name, id] : m_loadedModules) {
      uint32_t nameLen = static_cast<uint32_t>(name.size());
      out.write(reinterpret_cast<const char *>(&nameLen), sizeof(nameLen));
      out.write(name.data(), nameLen);
      out.write(reinterpret_cast<const char *>(&id), sizeof(id));
    }
    // Save emulator state with minimal lock scope
    {
      std::shared_lock<std::shared_mutex> lock(m_emulatorMutex);
      // RACE CONDITION FIX: Save atomic values individually
      uint64_t instructionsExecuted = m_stats.instructionsExecuted.load();
      uint64_t totalCycles = m_stats.totalCycles.load();
      uint64_t totalLatencyUs = m_stats.totalLatencyUs.load();
      uint64_t cacheHits = m_stats.cacheHits.load();
      uint64_t cacheMisses = m_stats.cacheMisses.load();
      uint64_t breakpointHits = m_stats.breakpointHits.load();

      out.write(reinterpret_cast<const char *>(&instructionsExecuted),
                sizeof(instructionsExecuted));
      out.write(reinterpret_cast<const char *>(&totalCycles),
                sizeof(totalCycles));
      out.write(reinterpret_cast<const char *>(&totalLatencyUs),
                sizeof(totalLatencyUs));
      out.write(reinterpret_cast<const char *>(&cacheHits), sizeof(cacheHits));
      out.write(reinterpret_cast<const char *>(&cacheMisses),
                sizeof(cacheMisses));
      out.write(reinterpret_cast<const char *>(&breakpointHits),
                sizeof(breakpointHits));
      out.write(reinterpret_cast<const char *>(&m_running), sizeof(m_running));
      out.write(reinterpret_cast<const char *>(&m_paused), sizeof(m_paused));
    }

    if (!out.good()) {
      throw std::runtime_error("Failed to write emulator state");
    }

    // Update statistics without lock (atomic operations)
    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);

    spdlog::info("SaveState: Saved to {}", path);
  } catch (const std::exception &e) {
    spdlog::error("SaveState failed: {}", e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
  }
}

/**
 * @brief Loads the emulator state.
 * @param path File path.
 */
void PS4Emulator::LoadState(const std::string &path) {
  auto start = std::chrono::steady_clock::now();

  // CRITICAL FIX: Minimize emulator mutex scope to prevent deadlocks with
  // component mutexes
  std::ifstream in;
  {
    std::unique_lock<std::shared_mutex> lock(m_emulatorMutex);
    in.open(path, std::ios::binary);
    if (!in) {
      spdlog::error("LoadState: Failed to open file: {}", path);
      throw PS4EmulatorException("Failed to load state");
    }
  }

  try {
    uint32_t version;
    in.read(reinterpret_cast<char *>(&version), sizeof(version));
    if (version != 1) {
      spdlog::error("Unsupported emulator state version: {}", version);
      throw std::runtime_error("Invalid emulator state version");
    }

    // Load component states without holding emulator mutex to prevent deadlocks
    for (auto &cpu : m_cpus) {
      cpu->LoadState(in);
    }
    m_mmu->LoadState(in);
    m_tlb->LoadState(in);
    for (auto &jit : m_jitCompilers) {
      jit->LoadState(in);
    }
    m_gpu->LoadState(in);
    m_tsc->LoadState(in);
    m_interruptHandler->LoadState(in);
    m_ioManager->LoadState(in);
    m_controllerManager->LoadState(in);
    m_audio->LoadState(in);
    m_os->LoadState(in);
    m_filesystem->LoadState(in);
    m_syscallHandler->LoadState(in);
    m_commandProcessor->LoadState(in);
    m_fiberManager->LoadState(in);
    m_trophyManager->LoadState(in);
    m_zlibWrapper->LoadState(in);
    uint64_t breakpointCount;
    in.read(reinterpret_cast<char *>(&breakpointCount),
            sizeof(breakpointCount));
    m_breakpoints.resize(breakpointCount);
    for (auto &bp : m_breakpoints) {
      in.read(reinterpret_cast<char *>(&bp.address), sizeof(bp.address));
      in.read(reinterpret_cast<char *>(&bp.enabled), sizeof(bp.enabled));
      uint32_t condLen;
      in.read(reinterpret_cast<char *>(&condLen), sizeof(condLen));
      bp.condition.resize(condLen);
      in.read(bp.condition.data(), condLen);
    }
    m_loadedModules.clear();
    uint64_t moduleCount;
    in.read(reinterpret_cast<char *>(&moduleCount), sizeof(moduleCount));
    for (uint64_t i = 0; i < moduleCount && in.good(); ++i) {
      uint32_t nameLen;
      in.read(reinterpret_cast<char *>(&nameLen), sizeof(nameLen));
      std::string name(nameLen, '\0');
      in.read(name.data(), nameLen);
      int id;
      in.read(reinterpret_cast<char *>(&id), sizeof(id));
      m_loadedModules[name] = id;
    }

    // Load emulator state with minimal lock scope
    {
      std::unique_lock<std::shared_mutex> lock(m_emulatorMutex);
      // RACE CONDITION FIX: Load atomic values individually
      uint64_t instructionsExecuted, totalCycles, totalLatencyUs, cacheHits,
          cacheMisses, breakpointHits;

      in.read(reinterpret_cast<char *>(&instructionsExecuted),
              sizeof(instructionsExecuted));
      in.read(reinterpret_cast<char *>(&totalCycles), sizeof(totalCycles));
      in.read(reinterpret_cast<char *>(&totalLatencyUs),
              sizeof(totalLatencyUs));
      in.read(reinterpret_cast<char *>(&cacheHits), sizeof(cacheHits));
      in.read(reinterpret_cast<char *>(&cacheMisses), sizeof(cacheMisses));
      in.read(reinterpret_cast<char *>(&breakpointHits),
              sizeof(breakpointHits));
      in.read(reinterpret_cast<char *>(&m_running), sizeof(m_running));
      in.read(reinterpret_cast<char *>(&m_paused), sizeof(m_paused));

      if (!in.good()) {
        throw std::runtime_error("Failed to read emulator state");
      }

      // Store loaded values into atomic members
      m_stats.instructionsExecuted.store(instructionsExecuted);
      m_stats.totalCycles.store(totalCycles);
      m_stats.totalLatencyUs.store(totalLatencyUs);
      m_stats.cacheHits.store(cacheHits);
      m_stats.cacheMisses.store(cacheMisses);
      m_stats.breakpointHits.store(breakpointHits);
    }

    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);

    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
    spdlog::info("LoadState: Loaded from {}, latency={}us", path, latency);
  } catch (const std::exception &e) {
    spdlog::error("LoadState failed: {}", e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
  }
}

/**
 * @brief Retrieves emulator statistics.
 * @return Current statistics.
 */
PS4Emulator::Stats PS4Emulator::GetStats() const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_emulatorMutex);
  try {
    // RACE CONDITION FIX: Create a copy with atomic loads
    Stats result;
    result.instructionsExecuted.store(m_stats.instructionsExecuted.load());
    result.totalCycles.store(m_stats.totalCycles.load());
    result.totalLatencyUs.store(m_stats.totalLatencyUs.load());
    result.cacheHits.store(m_stats.cacheHits.load());
    result.cacheMisses.store(m_stats.cacheMisses.load());
    result.breakpointHits.store(m_stats.breakpointHits.load());

    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
    return result;
  } catch (const std::exception &e) {
    spdlog::error("Get emulator stats failed: {}", e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
    return m_stats;
  }
}

/**
 * @brief Reconfigures the emulator.
 * @param cpuCount New CPU core count.
 * @param gpuSettings New GPU settings.
 * @return True on success, false otherwise.
 */
bool PS4Emulator::Reconfigure(uint32_t cpuCount,
                              const std::string &gpuSettings) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_emulatorMutex);
  try {
    if (m_running) {
      spdlog::warn("Cannot reconfigure while emulator is running");
      m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
      return false;
    }
    if (cpuCount < 1 || cpuCount > 16) {
      spdlog::warn("Invalid CPU count: {}", cpuCount);
      m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
      return false;
    }
    Stop();
    m_cpus.clear();
    m_jitCompilers.clear();
    for (uint32_t i = 0; i < cpuCount; ++i) {
      auto cpu = std::make_unique<x86_64::X86_64CPU>(*this, *m_mmu, i);
      m_jitCompilers.emplace_back(
          std::make_unique<x86_64::X86_64JITCompiler>(cpu.get()));
      m_cpus.push_back(std::move(cpu));
    }
    for (const auto &cpu : m_cpus) {
      if (!cpu->Initialize()) {
        spdlog::error("CPU initialization failed during reconfiguration");
        throw PS4EmulatorException("CPU initialization failed");
      }
    }

    // Parse GPU settings string (format: "key1=value1;key2=value2")
    if (!gpuSettings.empty()) {
      std::stringstream ss(gpuSettings);
      std::string setting;
      while (std::getline(ss, setting, ';')) {
        size_t eq = setting.find('=');
        if (eq != std::string::npos) {
          std::string key = setting.substr(0, eq);
          std::string value = setting.substr(eq + 1);

          // Trim whitespace
          key.erase(0, key.find_first_not_of(" \t"));
          key.erase(key.find_last_not_of(" \t") + 1);
          value.erase(0, value.find_first_not_of(" \t"));
          value.erase(value.find_last_not_of(" \t") + 1);

          if (key == "vsync") {
            bool vsync = (value == "true" || value == "1");
            spdlog::info("GPU setting: vsync={}", vsync);
            // Apply vsync setting to GPU
          } else if (key == "resolution") {
            spdlog::info("GPU setting: resolution={}", value);
            // Parse and apply resolution setting
          } else if (key == "anisotropy") {
            int aniso = std::stoi(value);
            spdlog::info("GPU setting: anisotropy={}", aniso);
            // Apply anisotropy setting
          } else if (key == "msaa") {
            int samples = std::stoi(value);
            spdlog::info("GPU setting: msaa={}x", samples);
            // Apply MSAA setting
          } else {
            spdlog::warn("Unknown GPU setting: {}={}", key, value);
          }
        }
      }
    }

    spdlog::info("Reconfigured emulator: cpuCount={}, gpuSettings={}", cpuCount,
                 gpuSettings);
    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("Reconfigure emulator failed: {}", e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
    return false;
  }
}

/**
 * @brief Enhanced expression parser for complex breakpoint conditions.
 * @details Supports boolean operators (&&, ||), parentheses, memory access
 * [address], arithmetic operations, and complex register comparisons.
 */
class BreakpointExpressionParser {
public:
  enum class TokenType {
    NUMBER,   // 123, 0x1234
    REGISTER, // rax, rbx, etc.
    MEMORY,   // [address] or [reg+offset]
    OPERATOR, // ==, !=, >, <, >=, <=, +, -, *, /, %
    LOGICAL,  // &&, ||
    LPAREN,   // (
    RPAREN,   // )
    LBRACKET, // [
    RBRACKET, // ]
    END       // End of input
  };

  struct Token {
    TokenType type;
    std::string value;
    uint64_t numValue = 0;
    x86_64::Register reg = x86_64::Register::NONE;
  };

private:
  std::string m_expression;
  x86_64::X86_64CPU &m_cpu;
  PS4MMU &m_mmu;
  std::vector<Token> m_tokens;
  size_t m_currentToken = 0;

public:
  BreakpointExpressionParser(const std::string &expr, x86_64::X86_64CPU &cpu,
                             PS4MMU &mmu)
      : m_expression(expr), m_cpu(cpu), m_mmu(mmu) {
    Tokenize();
  }

  bool Parse() {
    m_currentToken = 0;
    return ParseLogicalOr();
  }

private:
  void Tokenize() {
    m_tokens.clear();
    size_t pos = 0;

    while (pos < m_expression.length()) {
      // Skip whitespace
      while (pos < m_expression.length() && std::isspace(m_expression[pos])) {
        pos++;
      }

      if (pos >= m_expression.length())
        break;

      // Parse tokens
      if (m_expression[pos] == '(') {
        m_tokens.push_back({TokenType::LPAREN, "("});
        pos++;
      } else if (m_expression[pos] == ')') {
        m_tokens.push_back({TokenType::RPAREN, ")"});
        pos++;
      } else if (m_expression[pos] == '[') {
        m_tokens.push_back({TokenType::LBRACKET, "["});
        pos++;
      } else if (m_expression[pos] == ']') {
        m_tokens.push_back({TokenType::RBRACKET, "]"});
        pos++;
      } else if (pos + 1 < m_expression.length()) {
        // Check for two-character operators
        std::string twoChar = m_expression.substr(pos, 2);
        if (twoChar == "==" || twoChar == "!=" || twoChar == ">=" ||
            twoChar == "<=" || twoChar == "&&" || twoChar == "||") {
          TokenType type = (twoChar == "&&" || twoChar == "||")
                               ? TokenType::LOGICAL
                               : TokenType::OPERATOR;
          m_tokens.push_back({type, twoChar});
          pos += 2;
        } else {
          // Single character operators
          char ch = m_expression[pos];
          if (ch == '>' || ch == '<' || ch == '+' || ch == '-' || ch == '*' ||
              ch == '/' || ch == '%') {
            m_tokens.push_back({TokenType::OPERATOR, std::string(1, ch)});
            pos++;
          } else if (std::isdigit(ch) ||
                     (ch == '0' && pos + 1 < m_expression.length() &&
                      (m_expression[pos + 1] == 'x' ||
                       m_expression[pos + 1] == 'X'))) {
            // Parse number
            pos += ParseNumber(pos);
          } else if (std::isalpha(ch)) {
            // Parse register name
            pos += ParseRegister(pos);
          } else {
            // Unknown character, skip
            pos++;
          }
        }
      } else {
        // Single character at end
        char ch = m_expression[pos];
        if (ch == '>' || ch == '<' || ch == '+' || ch == '-' || ch == '*' ||
            ch == '/' || ch == '%') {
          m_tokens.push_back({TokenType::OPERATOR, std::string(1, ch)});
        }
        pos++;
      }
    }

    m_tokens.push_back({TokenType::END, ""});
  }

  size_t ParseNumber(size_t start) {
    size_t pos = start;
    std::string numStr;

    // Check for hex prefix
    if (pos + 1 < m_expression.length() && m_expression[pos] == '0' &&
        (m_expression[pos + 1] == 'x' || m_expression[pos + 1] == 'X')) {
      numStr += m_expression.substr(pos, 2);
      pos += 2;
      // Parse hex digits
      while (pos < m_expression.length() && std::isxdigit(m_expression[pos])) {
        numStr += m_expression[pos++];
      }
    } else {
      // Parse decimal digits
      while (pos < m_expression.length() && std::isdigit(m_expression[pos])) {
        numStr += m_expression[pos++];
      }
    }

    Token token;
    token.type = TokenType::NUMBER;
    token.value = numStr;

    if (numStr.substr(0, 2) == "0x" || numStr.substr(0, 2) == "0X") {
      token.numValue = std::stoull(numStr, nullptr, 16);
    } else {
      token.numValue = std::stoull(numStr, nullptr, 10);
    }

    m_tokens.push_back(token);
    return pos - start;
  }

  size_t ParseRegister(size_t start) {
    size_t pos = start;
    std::string regStr;

    // Parse register name (letters and digits)
    while (pos < m_expression.length() && (std::isalnum(m_expression[pos]))) {
      regStr += std::tolower(m_expression[pos++]);
    }

    Token token;
    token.type = TokenType::REGISTER;
    token.value = regStr;
    token.reg = ParseRegisterName(regStr);

    m_tokens.push_back(token);
    return pos - start;
  }

  x86_64::Register ParseRegisterName(const std::string &name) {
    if (name == "rax")
      return x86_64::Register::RAX;
    if (name == "rbx")
      return x86_64::Register::RBX;
    if (name == "rcx")
      return x86_64::Register::RCX;
    if (name == "rdx")
      return x86_64::Register::RDX;
    if (name == "rsi")
      return x86_64::Register::RSI;
    if (name == "rdi")
      return x86_64::Register::RDI;
    if (name == "rbp")
      return x86_64::Register::RBP;
    if (name == "rsp")
      return x86_64::Register::RSP;
    if (name == "r8")
      return x86_64::Register::R8;
    if (name == "r9")
      return x86_64::Register::R9;
    if (name == "r10")
      return x86_64::Register::R10;
    if (name == "r11")
      return x86_64::Register::R11;
    if (name == "r12")
      return x86_64::Register::R12;
    if (name == "r13")
      return x86_64::Register::R13;
    if (name == "r14")
      return x86_64::Register::R14;
    if (name == "r15")
      return x86_64::Register::R15;
    if (name == "rip")
      return x86_64::Register::RIP;
    if (name == "eax")
      return x86_64::Register::EAX;
    if (name == "ebx")
      return x86_64::Register::EBX;
    if (name == "ecx")
      return x86_64::Register::ECX;
    if (name == "edx")
      return x86_64::Register::EDX;
    return x86_64::Register::NONE;
  }

  // Recursive descent parser with proper operator precedence
  bool ParseLogicalOr() {
    bool left = ParseLogicalAnd();

    while (CurrentToken().type == TokenType::LOGICAL &&
           CurrentToken().value == "||") {
      ConsumeToken(); // consume ||
      bool right = ParseLogicalAnd();
      left = left || right;
    }

    return left;
  }

  bool ParseLogicalAnd() {
    bool left = ParseComparison();

    while (CurrentToken().type == TokenType::LOGICAL &&
           CurrentToken().value == "&&") {
      ConsumeToken(); // consume &&
      bool right = ParseComparison();
      left = left && right;
    }

    return left;
  }

  bool ParseComparison() {
    uint64_t left = ParseArithmetic();

    if (CurrentToken().type == TokenType::OPERATOR) {
      std::string op = CurrentToken().value;
      if (op == "==" || op == "!=" || op == ">" || op == "<" || op == ">=" ||
          op == "<=") {
        ConsumeToken();
        uint64_t right = ParseArithmetic();

        if (op == "==")
          return left == right;
        if (op == "!=")
          return left != right;
        if (op == ">")
          return left > right;
        if (op == "<")
          return left < right;
        if (op == ">=")
          return left >= right;
        if (op == "<=")
          return left <= right;
      }
    }

    // If no comparison operator, treat non-zero as true
    return left != 0;
  }

  uint64_t ParseArithmetic() {
    uint64_t left = ParseTerm();

    while (CurrentToken().type == TokenType::OPERATOR &&
           (CurrentToken().value == "+" || CurrentToken().value == "-")) {
      std::string op = CurrentToken().value;
      ConsumeToken();
      uint64_t right = ParseTerm();

      if (op == "+") {
        left += right;
      } else if (op == "-") {
        left -= right;
      }
    }

    return left;
  }

  uint64_t ParseTerm() {
    uint64_t left = ParseFactor();

    while (CurrentToken().type == TokenType::OPERATOR &&
           (CurrentToken().value == "*" || CurrentToken().value == "/" ||
            CurrentToken().value == "%")) {
      std::string op = CurrentToken().value;
      ConsumeToken();
      uint64_t right = ParseFactor();

      if (op == "*") {
        left *= right;
      } else if (op == "/" && right != 0) {
        left /= right;
      } else if (op == "%" && right != 0) {
        left %= right;
      }
    }

    return left;
  }

  uint64_t ParseFactor() {
    Token token = CurrentToken();

    if (token.type == TokenType::NUMBER) {
      ConsumeToken();
      return token.numValue;
    }

    if (token.type == TokenType::REGISTER) {
      ConsumeToken();
      if (token.reg != x86_64::Register::NONE) {
        return m_cpu.GetRegister(token.reg);
      }
      return 0;
    }

    if (token.type == TokenType::LBRACKET) {
      // Memory access [address] or [reg+offset]
      ConsumeToken(); // consume [
      uint64_t address = ParseArithmetic();

      if (CurrentToken().type == TokenType::RBRACKET) {
        ConsumeToken(); // consume ]

        // Read memory value
        try {
          uint64_t value = 0;
          m_mmu.ReadVirtual(address, &value, sizeof(uint64_t), 1);
          return value;
        } catch (...) {
          // Return 0 on memory read failure
          return 0;
        }
      }
      return 0;
    }

    if (token.type == TokenType::LPAREN) {
      ConsumeToken(); // consume (
      uint64_t result = ParseArithmetic();

      if (CurrentToken().type == TokenType::RPAREN) {
        ConsumeToken(); // consume )
      }

      return result;
    }

    return 0;
  }

  const Token &CurrentToken() const {
    if (m_currentToken < m_tokens.size()) {
      return m_tokens[m_currentToken];
    }
    static Token endToken{TokenType::END, ""};
    return endToken;
  }

  void ConsumeToken() {
    if (m_currentToken < m_tokens.size()) {
      m_currentToken++;
    }
  }
};

/**
 * @brief Sets the entry point for all CPU cores.
 * @param entryPoint The entry point address for execution.
 */
void PS4Emulator::SetCPUEntryPoints(uint64_t entryPoint) {
  std::lock_guard<std::shared_mutex> lock(m_emulatorMutex);

  spdlog::info("Setting CPU entry points to 0x{:x} for {} cores", entryPoint,
               m_cpus.size());

  for (size_t i = 0; i < m_cpus.size(); ++i) {
    if (m_cpus[i]) {
      // Set the RIP register to the entry point
      m_cpus[i]->SetRegister(x86_64::Register::RIP, entryPoint);

      // Initialize stack pointer for each CPU (offset each by 1MB)
      uint64_t stackBase = 0x7FFFF0000000ULL + (i * 0x100000);
      m_cpus[i]->SetRegister(x86_64::Register::RSP, stackBase);

      // Set up basic execution environment
      m_cpus[i]->SetRegister(x86_64::Register::RBP, stackBase);

      spdlog::debug("CPU {} entry point set: RIP=0x{:x}, RSP=0x{:x}", i,
                    entryPoint, stackBase);
    }
  }

  m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
}

/**
 * @brief Evaluates a breakpoint condition expression.
 * @param condition The condition string to evaluate.
 * @param cpu The CPU instance for register access.
 * @return True if condition is met, false otherwise.
 */
bool PS4Emulator::EvaluateBreakpointCondition(const std::string &condition,
                                              x86_64::X86_64CPU &cpu) {
  auto start = std::chrono::steady_clock::now();
  try {
    if (condition.empty()) {
      return true; // Empty condition always evaluates to true
    }

    // Enhanced recursive descent parser for complex expressions
    BreakpointExpressionParser parser(condition, cpu, *m_mmu);
    bool result = parser.Parse();

    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);

    spdlog::trace("Breakpoint condition '{}' evaluated to: {}", condition,
                  result ? "true" : "false");

    return result;
  } catch (const std::exception &e) {
    spdlog::error("EvaluateBreakpointCondition failed: {}", e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
    return true; // Default to true on error
  }
}

} // namespace ps4
