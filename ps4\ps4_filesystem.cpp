// Copyright 2025 <Copyright Owner>

#include "ps4_filesystem.h"
#include "../memory/memory_diagnostics.h"
#include "ps4_emulator.h"
#include <algorithm>
#include <chrono>
#include <fcntl.h>
#include <filesystem>
#include <fmt/core.h>
#include <fstream>
#include <iomanip>
#include <random>
#include <spdlog/spdlog.h>
#include <sstream>
#include <stdexcept>
#include <sys/stat.h>

// Placeholder for crypto subsystem (stubbed AES-256-CBC)
#include <openssl/aes.h>

#ifndef S_ISDIR
#define S_ISDIR(m) (((m) & S_IFMT) == S_IFDIR)
#endif

#ifdef _WIN32
#include <io.h>
#define stat _stat
#ifndef O_ACCMODE
#define O_ACCMODE (_O_RDONLY | _O_WRONLY | _O_RDWR)
#endif
#ifndef S_IWUSR
#define S_IWUSR _S_IWRITE
#endif

// Undefine conflicting Windows macros after including Windows headers
#ifdef CreateDirectoryA
#undef CreateDirectoryA
#endif
#ifdef CreateDirectoryW
#undef CreateDirectoryW
#endif
#ifdef CreateDirectory
#undef CreateDirectory
#endif
#ifdef RemoveDirectoryA
#undef RemoveDirectoryA
#endif
#ifdef RemoveDirectoryW
#undef RemoveDirectoryW
#endif
#ifdef RemoveDirectory
#undef RemoveDirectory
#endif
#ifdef EncryptFileA
#undef EncryptFileA
#endif
#ifdef EncryptFileW
#undef EncryptFileW
#endif
#ifdef EncryptFile
#undef EncryptFile
#endif
#ifdef DecryptFileA
#undef DecryptFileA
#endif
#ifdef DecryptFileW
#undef DecryptFileW
#endif
#ifdef DecryptFile
#undef DecryptFile
#endif

#else
#include <unistd.h>
#endif

namespace ps4 {

PS4Filesystem::PS4Filesystem(PS4Emulator &emu)
    : m_emulator(emu), m_rootPath(""), m_nextFd(3) {
  m_stats = FilesystemStats();
  spdlog::info("PS4Filesystem constructed");
}

PS4Filesystem::PS4Filesystem()
    : m_emulator(PS4Emulator::GetInstance()), m_rootPath(""), m_nextFd(3) {
  m_stats = FilesystemStats();
  spdlog::info("PS4Filesystem constructed (default)");
}

PS4Filesystem::~PS4Filesystem() {
  Shutdown();
  spdlog::info("PS4Filesystem destroyed");
}

bool PS4Filesystem::Initialize() {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_mutex);
  try {
    m_files.clear();
    m_fileHandles.clear();
    m_directories.clear();
    m_deviceFiles.clear();
    m_nextFd = 3;
    m_stats = FilesystemStats();
    m_directories.emplace_back("/");
    // Fix: Root path should be a host filesystem path, not a virtual mount point
    m_rootPath = "./ps4_root";
    std::filesystem::create_directories(m_rootPath);

    // Mount the root directory to /app0 for basic filesystem access
    m_mountPoints.Mount(std::filesystem::path(m_rootPath), "/app0");

    // Mount PKG installation directory - this is where installed PKG games will be stored
    std::filesystem::path pkgInstallPath = std::filesystem::path(m_rootPath) / "installed_packages";
    std::filesystem::create_directories(pkgInstallPath);
    m_mountPoints.Mount(pkgInstallPath, "/mnt/sandbox/pfsmnt");

    // Mount other standard directories
    m_mountPoints.Mount(std::filesystem::path(m_rootPath) / "dev", "/dev");
    m_mountPoints.Mount(std::filesystem::path(m_rootPath) / "savedata", m_settings.saveDataPath);
    m_mountPoints.Mount(std::filesystem::path(m_rootPath) / "trophy", m_settings.trophyPath);
    m_mountPoints.Mount(std::filesystem::path(m_rootPath) / "system", m_settings.systemPath);

    if (m_settings.enableDeviceFiles) {
      spdlog::info("Initializing device files...");
      InitializeDeviceFiles();
      m_handleTable.CreateStdHandles();
      spdlog::info("Device files initialized");
    }
    if (m_settings.enablePFS) {
      spdlog::info("Initializing PFS...");
      InitializePFSInternal();
      spdlog::info("PFS initialized");
    }

    lock.unlock();
    spdlog::info("Updating memory diagnostics...");
    ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
    spdlog::info("Memory diagnostics updated");
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start).count();
    spdlog::info("PS4Filesystem initialized with root path: {}", m_rootPath);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("PS4Filesystem initialization failed: {}", e.what());
    m_stats.cacheMisses++;
    return false;
  }
}

void PS4Filesystem::Shutdown() {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_mutex);
  try {
    for (auto &[fd, handle] : m_fileHandles) {
      if (handle.hostFd >= 3) ::close(handle.hostFd);
    }
    m_files.clear();
    m_fileHandles.clear();
    m_directories.clear();
    m_mountPoints.UnmountAll();
    m_stats = FilesystemStats();
    lock.unlock();
    ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start).count();
    spdlog::info("PS4Filesystem shutdown");
  } catch (const std::exception &e) {
    spdlog::error("PS4Filesystem shutdown failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

int PS4Filesystem::OpenFile(const std::string &path, int flags, mode_t mode) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_mutex);
  try {
    if (path.empty() || path[0] != '/') {
      spdlog::error("OpenFile: Invalid path: {}", path);
      errno = EINVAL;
      return -1;
    }

    bool is_read_only;
    std::filesystem::path hostPath = m_mountPoints.GetHostPath(path, &is_read_only);
    if (hostPath.empty()) {
      spdlog::error("OpenFile: Failed to map path: {}", path);
      errno = ENOENT;
      return -1;
    }
    if (is_read_only && (flags & (O_WRONLY | O_RDWR))) {
      spdlog::error("OpenFile: Write access denied for read-only path: {}", path);
      errno = EACCES;
      return -1;
    }

    if (flags & O_CREAT) {
      std::filesystem::create_directories(hostPath.parent_path());
    }

    int fd = m_handleTable.CreateHandle();
    HandleTable::File *file = m_handleTable.GetFile(fd);
    if (!file) {
      spdlog::error("OpenFile: Failed to create handle for: {}", path);
      errno = EMFILE;
      return -1;
    }

#ifdef _WIN32
    int hostFlags = (flags & O_ACCMODE) == O_RDONLY ? _O_RDONLY :
                    (flags & O_ACCMODE) == O_WRONLY ? _O_WRONLY : _O_RDWR;
    if (flags & O_APPEND) hostFlags |= _O_APPEND;
    if (flags & O_CREAT) hostFlags |= _O_CREAT;
    if (flags & O_TRUNC) hostFlags |= _O_TRUNC;
    if (flags & O_EXCL) hostFlags |= _O_EXCL;
    hostFlags |= _O_BINARY;
    int pmode = _S_IREAD | (mode & S_IWUSR ? _S_IWRITE : 0);
    file->host_fd = ::open(hostPath.string().c_str(), hostFlags, pmode);
#else
    file->host_fd = ::open(hostPath.c_str(), flags, mode);
#endif

    if (file->host_fd < 0) {
      m_handleTable.DeleteHandle(fd);
      spdlog::error("OpenFile: Failed to open {}: errno={}", hostPath.string(), errno);
      m_stats.cacheMisses++;
      return -1;
    }

    file->is_opened = true;
    file->host_name = hostPath;
    file->guest_name = path;
    file->type = DetermineFileType(path);

    FileEntry &entry = m_files[path];
    entry.path = path;
    entry.hostPath = hostPath.string();
    struct stat st;
    if (::stat(hostPath.string().c_str(), &st) == 0) {
      entry.size = st.st_size;
      entry.mode = st.st_mode;
      entry.creationTime = st.st_ctime;
      entry.modificationTime = st.st_mtime;
      entry.accessTime = st.st_atime;
      entry.isDir = S_ISDIR(st.st_mode);
    } else {
      entry.size = 0;
      entry.mode = mode;
      entry.creationTime = std::time(nullptr);
      entry.modificationTime = entry.creationTime;
      entry.accessTime = entry.creationTime;
    }
    entry.fileType = file->type;

    m_stats.operationCount++;
    m_stats.fileAccessCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start).count();
    spdlog::info("OpenFile: Opened {} as FD {}, hostPath={}", path, fd, hostPath.string());
    return fd;
  } catch (const std::exception &e) {
    spdlog::error("OpenFile failed: {}", e.what());
    errno = EIO;
    m_stats.cacheMisses++;
    return -1;
  }
}

int PS4Filesystem::CloseFile(int fd) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_mutex);
  try {
    HandleTable::File *file = m_handleTable.GetFile(fd);
    if (!file || !file->is_opened) {
      spdlog::error("CloseFile: Invalid FD: {}", fd);
      errno = EBADF;
      return -1;
    }
    if (file->host_fd >= 3) {
      ::close(file->host_fd);
    }
    m_handleTable.DeleteHandle(fd);
    m_stats.operationCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start).count();
    spdlog::info("CloseFile: Closed FD {}", fd);
    return 0;
  } catch (const std::exception &e) {
    spdlog::error("CloseFile failed: {}", e.what());
    errno = EIO;
    m_stats.cacheMisses++;
    return -1;
  }
}

ssize_t PS4Filesystem::ReadFile(int fd, void *buf, size_t count) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_mutex);
  try {
    HandleTable::File *file = m_handleTable.GetFile(fd);
    if (!file || !file->is_opened) {
      spdlog::error("ReadFile: Invalid FD: {}", fd);
      errno = EBADF;
      return -1;
    }

    auto fileIt = m_files.find(file->guest_name);
    if (fileIt != m_files.end() && !fileIt->second.data.empty()) {
      size_t bytesToRead = std::min(count, fileIt->second.data.size() - fileIt->second.size);
      std::memcpy(buf, fileIt->second.data.data() + fileIt->second.size, bytesToRead);
      fileIt->second.size += bytesToRead;
      fileIt->second.accessTime = std::time(nullptr);
      fileIt->second.cacheHits++;
      m_stats.cacheHits++;
      m_stats.operationCount++;
      m_stats.fileAccessCount++;
      auto end = std::chrono::steady_clock::now();
      m_stats.totalLatencyUs +=
          std::chrono::duration_cast<std::chrono::microseconds>(end - start).count();
      spdlog::trace("ReadFile: Read {} bytes from FD {} (cached)", bytesToRead, fd);
      return static_cast<ssize_t>(bytesToRead);
    }

    lock.unlock();
    if (file->type == PS4FileType::Device) {
      if (HandleDeviceAccess(file->guest_name, buf, count, false)) {
        m_stats.cacheHits++;
        return static_cast<ssize_t>(count);
      }
      errno = EIO;
      return -1;
    }

    ssize_t bytesRead = ::read(file->host_fd, buf, count);
    lock.lock();
    if (bytesRead < 0) {
      spdlog::error("ReadFile: Failed for FD {}: errno={}", fd, errno);
      m_stats.cacheMisses++;
      return -1;
    }

    if (fileIt != m_files.end()) {
      fileIt->second.accessTime = std::time(nullptr);
      if (fileIt->second.data.empty() && bytesRead > 0) {
        fileIt->second.data.resize(bytesRead);
        std::memcpy(fileIt->second.data.data(), buf, bytesRead);
      }
      fileIt->second.size += bytesRead;
    }
    m_stats.operationCount++;
    m_stats.fileAccessCount++;
    m_stats.cacheMisses++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start).count();
    spdlog::trace("ReadFile: Read {} bytes from FD {}", bytesRead, fd);
    return bytesRead;
  } catch (const std::exception &e) {
    spdlog::error("ReadFile failed: {}", e.what());
    errno = EIO;
    m_stats.cacheMisses++;
    return -1;
  }
}

bool PS4Filesystem::ReadFile(const std::string &path, std::vector<uint8_t> &data) {
  std::string hostPath;
  {
    std::lock_guard<std::shared_mutex> lock(m_mutex);
    hostPath = MapToHostPath(path);
  }
  try {
    std::ifstream file(hostPath, std::ios::binary | std::ios::ate);
    if (!file.is_open()) {
      spdlog::error("ReadFile: Cannot open file: {}", path);
      errno = ENOENT;
      return false;
    }

    std::streamsize size = file.tellg();
    file.seekg(0, std::ios::beg);
    data.resize(static_cast<size_t>(size));
    if (!file.read(reinterpret_cast<char *>(data.data()), size)) {
      spdlog::error("ReadFile: Failed to read file: {}", path);
      errno = EIO;
      return false;
    }

    spdlog::trace("ReadFile: Successfully read {} bytes from {}", size, path);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("ReadFile failed for {}: {}", path, e.what());
    errno = EIO;
    return false;
  }
}

// Replace existing ListFiles implementation with this:
std::vector<std::string> PS4Filesystem::ListFiles(const std::string &virtDir,
                                                  bool recursive) const {
    std::vector<std::string> result;
    // Map the PS4 virtual directory to the host filesystem path
    std::string hostDir = MapToHostPath(virtDir);
    if (!std::filesystem::exists(hostDir)) {
        return result;
    }
    // Choose recursive iterator if requested
    auto opts = std::filesystem::directory_options::skip_permission_denied;
    if (recursive) {
        for (auto &entry : std::filesystem::recursive_directory_iterator(hostDir, opts)) {
            if (!entry.is_regular_file()) continue;
            std::string hostFile = entry.path().string();
            // Build virtual path: virtDir + relative portion
            std::string rel = hostFile.substr(hostDir.length());
            // normalize separators to forward slash
            std::replace(rel.begin(), rel.end(), '\\', '/');
            std::string virtPath = virtDir;
            if (!virtPath.empty() && virtPath.back() != '/') virtPath += '/';
            virtPath += (rel.front() == '/' ? rel.substr(1) : rel);
            result.push_back(virtPath);
        }
    } else {
        for (auto &entry : std::filesystem::directory_iterator(hostDir, opts)) {
            if (!entry.is_regular_file()) continue;
            std::string hostFile = entry.path().string();
            std::string rel = hostFile.substr(hostDir.length());
            std::replace(rel.begin(), rel.end(), '\\', '/');
            std::string virtPath = virtDir;
            if (!virtPath.empty() && virtPath.back() != '/') virtPath += '/';
            virtPath += (rel.front() == '/' ? rel.substr(1) : rel);
            result.push_back(virtPath);
        }
    }
    return result;
}

ssize_t PS4Filesystem::WriteFile(int fd, const void *buf, size_t count) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_mutex);
  try {
    HandleTable::File *file = m_handleTable.GetFile(fd);
    if (!file || !file->is_opened) {
      spdlog::error("WriteFile: Invalid FD: {}", fd);
      errno = EBADF;
      return -1;
    }

    auto fileIt = m_files.find(file->guest_name);
    lock.unlock();
    if (file->type == PS4FileType::Device) {
      if (HandleDeviceAccess(file->guest_name, const_cast<void *>(buf), count, true)) {
        m_stats.cacheHits++;
        return static_cast<ssize_t>(count);
      }
      errno = EIO;
      return -1;
    }

    ssize_t bytesWritten = ::write(file->host_fd, buf, count);
    lock.lock();
    if (bytesWritten < 0) {
      spdlog::error("WriteFile: Failed for FD {}: errno={}", fd, errno);
      m_stats.cacheMisses++;
      return -1;
    }

    if (fileIt != m_files.end()) {
      fileIt->second.size = std::max(fileIt->second.size, fileIt->second.size + bytesWritten);
      fileIt->second.modificationTime = std::time(nullptr);
      if (!fileIt->second.data.empty()) {
        fileIt->second.data.resize(fileIt->second.size);
        std::memcpy(fileIt->second.data.data() + (fileIt->second.size - bytesWritten), buf, bytesWritten);
        fileIt->second.cacheHits++;
      }
    }
    m_stats.operationCount++;
    m_stats.fileAccessCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start).count();
    spdlog::trace("WriteFile: Wrote {} bytes to FD {}", bytesWritten, fd);
    return bytesWritten;
  } catch (const std::exception &e) {
    spdlog::error("WriteFile failed: {}", e.what());
    errno = EIO;
    m_stats.cacheMisses++;
    return -1;
  }
}

bool PS4Filesystem::WriteFile(const std::string &path, const void *data, size_t size) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_mutex);
  try {
    if (path.empty() || path[0] != '/') {
      spdlog::error("WriteFile: Invalid path: {}", path);
      errno = EINVAL;
      m_stats.cacheMisses++;
      return false;
    }

    bool is_read_only;
    std::filesystem::path hostPath = m_mountPoints.GetHostPath(path, &is_read_only);
    if (hostPath.empty()) {
      spdlog::error("WriteFile: Failed to map path: {}", path);
      errno = ENOENT;
      m_stats.cacheMisses++;
      return false;
    }
    if (is_read_only) {
      spdlog::error("WriteFile: Write access denied for read-only path: {}", path);
      errno = EACCES;
      m_stats.cacheMisses++;
      return false;
    }

    std::filesystem::create_directories(hostPath.parent_path());
    lock.unlock();

#ifdef _WIN32
    int hostFd = ::open(hostPath.string().c_str(), _O_WRONLY | _O_CREAT | _O_TRUNC | _O_BINARY,
                        _S_IREAD | _S_IWRITE);
#else
    int hostFd = ::open(hostPath.c_str(), O_WRONLY | O_CREAT | O_TRUNC, 0644);
#endif

    if (hostFd < 0) {
      spdlog::error("WriteFile: Failed to open {}: errno={}", hostPath.string(), errno);
      m_stats.cacheMisses++;
      return false;
    }

    ssize_t bytesWritten = ::write(hostFd, data, size);
    ::close(hostFd);
    lock.lock();

    if (bytesWritten != static_cast<ssize_t>(size)) {
      spdlog::error("WriteFile: Wrote {} bytes, expected {}", bytesWritten, size);
      m_stats.cacheMisses++;
      return false;
    }

    FileEntry &entry = m_files[path];
    entry.path = path;
    entry.hostPath = hostPath.string();
    entry.size = size;
    entry.mode = 0644;
    entry.modificationTime = std::time(nullptr);
    entry.present = true;
    entry.isDir = false;
    entry.fileType = PS4FileType::Regular;

    m_stats.operationCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start).count();
    spdlog::debug("WriteFile: Wrote {} bytes to {}", size, path);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("WriteFile failed for {}: {}", path, e.what());
    errno = EIO;
    m_stats.cacheMisses++;
    return false;
  }
}

off_t PS4Filesystem::SeekFile(int fd, off_t offset, int whence) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_mutex);
  try {
    HandleTable::File *file = m_handleTable.GetFile(fd);
    if (!file || !file->is_opened) {
      spdlog::error("SeekFile: Invalid FD: {}", fd);
      errno = EBADF;
      return -1;
    }

    lock.unlock();
    off_t newOffset = ::lseek(file->host_fd, offset, whence);
    lock.lock();
    if (newOffset < 0) {
      spdlog::error("SeekFile: Failed for FD {}: errno={}", fd, errno);
      m_stats.cacheMisses++;
      return -1;
    }

    auto fileIt = m_files.find(file->guest_name);
    if (fileIt != m_files.end()) {
      fileIt->second.size = newOffset;
    }
    m_stats.operationCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start).count();
    spdlog::trace("SeekFile: FD {}, new offset={}", fd, newOffset);
    return newOffset;
  } catch (const std::exception &e) {
    spdlog::error("SeekFile failed: {}", e.what());
    errno = EIO;
    m_stats.cacheMisses++;
    return -1;
  }
}

#ifdef _WIN32
int PS4Filesystem::StatFile(const std::string &path, struct _stat64i32 *buf) {
#else
int PS4Filesystem::StatFile(const std::string &path, struct stat *buf) {
#endif
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_mutex);
  try {
    auto it = m_files.find(path);
    if (it != m_files.end()) {
      buf->st_size = it->second.size;
      buf->st_mode = it->second.mode;
      buf->st_ctime = it->second.creationTime;
      buf->st_mtime = it->second.modificationTime;
      buf->st_atime = it->second.accessTime;
      it->second.cacheHits++;
      m_stats.cacheHits++;
      m_stats.operationCount++;
      m_stats.fileAccessCount++;
      auto end = std::chrono::steady_clock::now();
      m_stats.totalLatencyUs +=
          std::chrono::duration_cast<std::chrono::microseconds>(end - start).count();
      spdlog::trace(fmt::format("StatFile: Path {} (cached), size={}", path, it->second.size));
      return 0;
    }

    std::filesystem::path hostPath = m_mountPoints.GetHostPath(path);
    if (hostPath.empty()) {
      spdlog::error("StatFile: Failed to map path: {}", path);
      errno = ENOENT;
      return -1;
    }

    lock.unlock();
    if (::stat(hostPath.string().c_str(), buf) < 0) {
      spdlog::error("StatFile: Path not found: {}, errno={}", hostPath.string(), errno);
      m_stats.cacheMisses++;
      return -1;
    }
    lock.lock();

    m_stats.operationCount++;
    m_stats.fileAccessCount++;
    m_stats.cacheMisses++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start).count();
    spdlog::trace("StatFile: Path {}, size={}", hostPath.string(), buf->st_size);
    return 0;
  } catch (const std::exception &e) {
    spdlog::error("StatFile failed: {}", e.what());
    errno = EIO;
    m_stats.cacheMisses++;
    return -1;
  }
}

bool PS4Filesystem::CreateDirectory(const std::string &path, mode_t mode) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_mutex);
  try {
    std::filesystem::path hostPath = m_mountPoints.GetHostPath(path);
    if (hostPath.empty()) {
      spdlog::error("CreateDirectory: Failed to map path: {}", path);
      errno = ENOENT;
      return false;
    }

    lock.unlock();
    std::filesystem::create_directories(hostPath);
    lock.lock();

    struct stat st;
    if (::stat(hostPath.string().c_str(), &st) == 0) {
      m_directories.push_back(path);
      FileEntry &entry = m_files[path];
      entry.path = path;
      entry.hostPath = hostPath.string();
      entry.size = 0;
      entry.mode = mode ? mode : m_settings.defaultDirMode;
      entry.creationTime = st.st_ctime;
      entry.modificationTime = st.st_mtime;
      entry.accessTime = st.st_atime;
      entry.isDir = true;
      entry.fileType = PS4FileType::Directory;
      entry.cacheHits++;
    }
    m_stats.operationCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start).count();
    spdlog::trace("CreateDirectory: path={}", path);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("CreateDirectory failed: {}", e.what());
    errno = EIO;
    m_stats.cacheMisses++;
    return false;
  }
}

bool PS4Filesystem::CreateDirectoryW(const std::wstring &path) {
  return MountDirectory(path);
}

bool PS4Filesystem::CreateVirtualDirectory(const std::string &path) {
  try {
    std::unique_lock<std::shared_mutex> lock(m_mutex);
    if (std::find(m_directories.begin(), m_directories.end(), path) == m_directories.end()) {
      m_directories.push_back(path);
    }

    FileEntry &entry = m_files[path];
    entry.path = path;
    entry.hostPath = MapToHostPath(path);
    entry.fileType = PS4FileType::Directory;
    entry.size = 0;
    entry.mode = 0755;
    entry.modificationTime = std::time(nullptr);
    entry.present = true;
    entry.isDir = true;

    std::filesystem::create_directories(entry.hostPath);
    spdlog::debug("Created virtual directory: {}", path);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("CreateVirtualDirectory failed for {}: {}", path, e.what());
    errno = EIO;
    return false;
  }
}

bool PS4Filesystem::RemoveDirectory(const std::string &path) {
  try {
    std::filesystem::path hostPath = m_mountPoints.GetHostPath(path);
    if (hostPath.empty()) {
      spdlog::error("RemoveDirectory: Failed to map path: {}", path);
      errno = ENOENT;
      return false;
    }

    if (!std::filesystem::exists(hostPath)) {
      spdlog::warn("Directory does not exist: {}", path);
      return true;
    }
    if (!std::filesystem::is_directory(hostPath)) {
      spdlog::error("Path is not a directory: {}", path);
      errno = ENOTDIR;
      return false;
    }

    std::filesystem::remove_all(hostPath);
    std::unique_lock<std::shared_mutex> lock(m_mutex);
    m_directories.erase(std::remove(m_directories.begin(), m_directories.end(), path),
                        m_directories.end());
    m_files.erase(path);
    spdlog::info("Removed directory: {}", path);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("RemoveDirectory failed for {}: {}", path, e.what());
    errno = EIO;
    return false;
  }
}

bool PS4Filesystem::MountDirectory(const std::wstring &path) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_mutex);
  try {
    std::filesystem::path hostPath(path);
    std::string guestPath = "/mnt/" + hostPath.filename().string();
    m_mountPoints.Mount(hostPath, guestPath);
    m_directories.push_back(guestPath);
    m_stats.operationCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start).count();
    spdlog::info("MountDirectory: path={}", guestPath);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("MountDirectory failed: {}", e.what());
    errno = EIO;
    m_stats.cacheMisses++;
    return false;
  }
}

uint64_t PS4Filesystem::AllocateVirtualMemory(uint64_t size, uint64_t alignment, bool shared) {
  auto start = std::chrono::steady_clock::now();
  try {
    uint64_t addr = m_emulator.GetOrbisOS().AllocateVirtualMemory(size, alignment, shared);
    m_stats.operationCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start).count();
    return addr;
  } catch (const std::exception &e) {
    spdlog::error("AllocateVirtualMemory failed: {}", e.what());
    m_stats.cacheMisses++;
    return 0;
  }
}

bool PS4Filesystem::FreeVirtualMemory(uint64_t address) {
  auto start = std::chrono::steady_clock::now();
  try {
    m_emulator.GetOrbisOS().FreeVirtualMemory(address);
    m_stats.operationCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start).count();
    return true;
  } catch (const std::exception &e) {
    spdlog::error("FreeVirtualMemory failed: {}", e.what());
    m_stats.cacheMisses++;
    return false;
  }
}

bool PS4Filesystem::ProtectMemory(uint64_t address, uint64_t size, int protection) {
  auto start = std::chrono::steady_clock::now();
  try {
    bool success = m_emulator.GetOrbisOS().ProtectMemory(address, size, protection);
    m_stats.operationCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start).count();
    return success;
  } catch (const std::exception &e) {
    spdlog::error("ProtectMemory failed: {}", e.what());
    m_stats.cacheMisses++;
    return false;
  }
}

uint64_t PS4Filesystem::SceKernelGetProcessId() {
  auto start = std::chrono::steady_clock::now();
  try {
    uint64_t pid = m_emulator.GetOrbisOS().SceKernelGetProcessId();
    m_stats.operationCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start).count();
    return pid;
  } catch (const std::exception &e) {
    spdlog::error("SceKernelGetProcessId failed: {}", e.what());
    m_stats.cacheMisses++;
    return 1;
  }
}

std::string PS4Filesystem::DumpState() const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_mutex);
  try {
    std::ostringstream oss;
    oss << "PS4 Filesystem State Dump\n";
    oss << "Root Path: " << m_rootPath << "\n";
    oss << "Open File Handles: " << m_fileHandles.size() << "\n";
    oss << "Total Files: " << m_files.size() << "\n";
    oss << "Directories: " << m_directories.size() << "\n";
    oss << "Device Files: " << m_deviceFiles.size() << "\n";
    oss << "Stats: Ops=" << m_stats.operationCount
        << ", Latency=" << m_stats.totalLatencyUs << "us, "
        << "Hits=" << m_stats.cacheHits << ", Misses=" << m_stats.cacheMisses
        << ", Accesses=" << m_stats.fileAccessCount << "\n";
    oss << "Game Loaded: " << (m_gameLoaded ? "Yes" : "No");
    if (m_gameLoaded) oss << " (" << m_loadedGamePath << ")";
    oss << "\n";
    lock.unlock();
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start).count();
    return oss.str();
  } catch (const std::exception &e) {
    spdlog::error("DumpState failed: {}", e.what());
    return "Error dumping state: " + std::string(e.what());
  }
}

void PS4Filesystem::SaveState(std::ostream &out) const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_mutex);
  try {
    uint64_t fileCount = m_files.size();
    out.write(reinterpret_cast<const char *>(&fileCount), sizeof(fileCount));
    for (const auto &[path, entry] : m_files) {
      uint64_t pathLen = path.size();
      out.write(reinterpret_cast<const char *>(&pathLen), sizeof(pathLen));
      out.write(path.c_str(), pathLen);
      uint64_t hostPathLen = entry.hostPath.size();
      out.write(reinterpret_cast<const char *>(&hostPathLen), sizeof(hostPathLen));
      out.write(entry.hostPath.c_str(), hostPathLen);
      out.write(reinterpret_cast<const char *>(&entry.size), sizeof(entry.size));
      out.write(reinterpret_cast<const char *>(&entry.protection), sizeof(entry.protection));
      out.write(reinterpret_cast<const char *>(&entry.mode), sizeof(entry.mode));
      out.write(reinterpret_cast<const char *>(&entry.creationTime), sizeof(entry.creationTime));
      out.write(reinterpret_cast<const char *>(&entry.modificationTime), sizeof(entry.modificationTime));
      out.write(reinterpret_cast<const char *>(&entry.accessTime), sizeof(entry.accessTime));
      out.write(reinterpret_cast<const char *>(&entry.isDir), sizeof(entry.isDir));
      uint64_t dataSize = entry.data.size();
      out.write(reinterpret_cast<const char *>(&dataSize), sizeof(dataSize));
      if (dataSize > 0) {
        out.write(reinterpret_cast<const char *>(entry.data.data()), dataSize);
      }
      out.write(reinterpret_cast<const char *>(&entry.fileType), sizeof(entry.fileType));
      out.write(reinterpret_cast<const char *>(&entry.ps4Permissions), sizeof(entry.ps4Permissions));
      uint64_t mountPointLen = entry.mountPoint.size();
      out.write(reinterpret_cast<const char *>(&mountPointLen), sizeof(mountPointLen));
      out.write(entry.mountPoint.c_str(), mountPointLen);
      out.write(reinterpret_cast<const char *>(&entry.isEncrypted), sizeof(entry.isEncrypted));
      out.write(reinterpret_cast<const char *>(&entry.blockSize), sizeof(entry.blockSize));
      uint64_t checksumSize = entry.checksum.size();
      out.write(reinterpret_cast<const char *>(&checksumSize), sizeof(checksumSize));
      if (checksumSize > 0) {
        out.write(reinterpret_cast<const char *>(entry.checksum.data()), checksumSize);
      }
      out.write(reinterpret_cast<const char *>(&entry.present), sizeof(entry.present));
      out.write(reinterpret_cast<const char *>(&entry.cacheHits), sizeof(entry.cacheHits));
      out.write(reinterpret_cast<const char *>(&entry.cacheMisses), sizeof(entry.cacheMisses));
    }
    uint64_t handleCount = m_fileHandles.size();
    out.write(reinterpret_cast<const char *>(&handleCount), sizeof(handleCount));
    for (const auto &[fd, handle] : m_fileHandles) {
      out.write(reinterpret_cast<const char *>(&fd), sizeof(fd));
      uint64_t pathLen = handle.path.size();
      out.write(reinterpret_cast<const char *>(&pathLen), sizeof(pathLen));
      out.write(handle.path.c_str(), pathLen);
      out.write(reinterpret_cast<const char *>(&handle.flags), sizeof(handle.flags));
      out.write(reinterpret_cast<const char *>(&handle.offset), sizeof(handle.offset));
      out.write(reinterpret_cast<const char *>(&handle.hostFd), sizeof(handle.hostFd));
      out.write(reinterpret_cast<const char *>(&handle.fd), sizeof(handle.fd));
      out.write(reinterpret_cast<const char *>(&handle.cacheHits), sizeof(handle.cacheHits));
      out.write(reinterpret_cast<const char *>(&handle.cacheMisses), sizeof(handle.cacheMisses));
    }
    uint64_t dirCount = m_directories.size();
    out.write(reinterpret_cast<const char *>(&dirCount), sizeof(dirCount));
    for (const auto &dir : m_directories) {
      uint64_t dirLen = dir.size();
      out.write(reinterpret_cast<const char *>(&dirLen), sizeof(dirLen));
      out.write(dir.c_str(), dirLen);
    }
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start).count();
    spdlog::info("PS4Filesystem state saved");
  } catch (const std::exception &e) {
    spdlog::error("SaveState failed: {}", e.what());
  }
}

void PS4Filesystem::LoadState(std::istream &in) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_mutex);
  try {
    m_files.clear();
    m_fileHandles.clear();
    m_directories.clear();
    uint64_t fileCount;
    in.read(reinterpret_cast<char *>(&fileCount), sizeof(fileCount));
    for (uint64_t i = 0; i < fileCount; ++i) {
      uint64_t pathLen;
      in.read(reinterpret_cast<char *>(&pathLen), sizeof(pathLen));
      std::string path(pathLen, '\0');
      in.read(&path[0], pathLen);
      FileEntry entry;
      entry.path = path;
      uint64_t hostPathLen;
      in.read(reinterpret_cast<char *>(&hostPathLen), sizeof(hostPathLen));
      entry.hostPath.resize(hostPathLen);
      in.read(&entry.hostPath[0], hostPathLen);
      in.read(reinterpret_cast<char *>(&entry.size), sizeof(entry.size));
      in.read(reinterpret_cast<char *>(&entry.protection), sizeof(entry.protection));
      in.read(reinterpret_cast<char *>(&entry.mode), sizeof(entry.mode));
      in.read(reinterpret_cast<char *>(&entry.creationTime), sizeof(entry.creationTime));
      in.read(reinterpret_cast<char *>(&entry.modificationTime), sizeof(entry.modificationTime));
      in.read(reinterpret_cast<char *>(&entry.accessTime), sizeof(entry.accessTime));
      in.read(reinterpret_cast<char *>(&entry.isDir), sizeof(entry.isDir));
      uint64_t dataSize;
      in.read(reinterpret_cast<char *>(&dataSize), sizeof(dataSize));
      entry.data.resize(dataSize);
      if (dataSize > 0) {
        in.read(reinterpret_cast<char *>(entry.data.data()), dataSize);
      }
      in.read(reinterpret_cast<char *>(&entry.fileType), sizeof(entry.fileType));
      in.read(reinterpret_cast<char *>(&entry.ps4Permissions), sizeof(entry.ps4Permissions));
      uint64_t mountPointLen;
      in.read(reinterpret_cast<char *>(&mountPointLen), sizeof(mountPointLen));
      entry.mountPoint.resize(mountPointLen);
      in.read(&entry.mountPoint[0], mountPointLen);
      in.read(reinterpret_cast<char *>(&entry.isEncrypted), sizeof(entry.isEncrypted));
      in.read(reinterpret_cast<char *>(&entry.blockSize), sizeof(entry.blockSize));
      uint64_t checksumSize;
      in.read(reinterpret_cast<char *>(&checksumSize), sizeof(checksumSize));
      entry.checksum.resize(checksumSize);
      if (checksumSize > 0) {
        in.read(reinterpret_cast<char *>(entry.checksum.data()), checksumSize);
      }
      in.read(reinterpret_cast<char *>(&entry.present), sizeof(entry.present));
      in.read(reinterpret_cast<char *>(&entry.cacheHits), sizeof(entry.cacheHits));
      in.read(reinterpret_cast<char *>(&entry.cacheMisses), sizeof(entry.cacheMisses));
      m_files[path] = entry;
    }
    uint64_t handleCount;
    in.read(reinterpret_cast<char *>(&handleCount), sizeof(handleCount));
    for (uint64_t i = 0; i < handleCount; ++i) {
      int fd;
      in.read(reinterpret_cast<char *>(&fd), sizeof(fd));
      FileHandle handle;
      uint64_t pathLen;
      in.read(reinterpret_cast<char *>(&pathLen), sizeof(pathLen));
      handle.path.resize(pathLen);
      in.read(&handle.path[0], pathLen);
      in.read(reinterpret_cast<char *>(&handle.flags), sizeof(handle.flags));
      in.read(reinterpret_cast<char *>(&handle.offset), sizeof(handle.offset));
      in.read(reinterpret_cast<char *>(&handle.hostFd), sizeof(handle.hostFd));
      in.read(reinterpret_cast<char *>(&handle.fd), sizeof(handle.fd));
      in.read(reinterpret_cast<char *>(&handle.cacheHits), sizeof(handle.cacheHits));
      in.read(reinterpret_cast<char *>(&handle.cacheMisses), sizeof(handle.cacheMisses));
      m_fileHandles[fd] = handle;
      m_nextFd = std::max(m_nextFd, fd + 1);
    }
    uint64_t dirCount;
    in.read(reinterpret_cast<char *>(&dirCount), sizeof(dirCount));
    for (uint64_t i = 0; i < dirCount; ++i) {
      uint64_t dirLen;
      in.read(reinterpret_cast<char *>(&dirLen), sizeof(dirLen));
      std::string dir(dirLen, '\0');
      in.read(&dir[0], dirLen);
      m_directories.push_back(dir);
    }
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start).count();
    spdlog::info("PS4Filesystem state loaded");
  } catch (const std::exception &e) {
    spdlog::error("LoadState failed: {}", e.what());
  }
}

FilesystemStats PS4Filesystem::GetStats() const {
  std::shared_lock<std::shared_mutex> lock(m_mutex);
  try {
    FilesystemStats currentStats = m_stats;
    return currentStats;
  } catch (const std::exception &e) {
    spdlog::error("GetStats failed: {}", e.what());
    return m_stats;
  }
}

void PS4Filesystem::SetSettings(const EnhancedSettings &settings) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_mutex);
  try {
    m_settings = settings;
    m_stats.operationCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start).count();
    spdlog::info("SetSettings: Updated settings");
  } catch (const std::exception &e) {
    spdlog::error("SetSettings failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

const EnhancedSettings &PS4Filesystem::GetSettings() const {
  std::shared_lock<std::shared_mutex> lock(m_mutex);
  try {
    return m_settings;
  } catch (const std::exception &e) {
    spdlog::error("GetSettings failed: {}", e.what());
    return m_settings;
  }
}

bool PS4Filesystem::SaveSettings(const std::string &filename) const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_mutex);
  try {
    std::ofstream out(filename, std::ios::binary);
    if (!out) {
      spdlog::error("SaveSettings: Cannot open file: {}", filename);
      errno = EIO;
      return false;
    }
    out.write(reinterpret_cast<const char *>(&m_settings), sizeof(m_settings));
    out.close();
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start).count();
    spdlog::info("PS4Filesystem settings saved to: {}", filename);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("SaveSettings failed: {}", e.what());
    errno = EIO;
    return false;
  }
}

bool PS4Filesystem::LoadSettings(const std::string &filename) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_mutex);
  try {
    std::ifstream in(filename, std::ios::binary);
    if (!in) {
      spdlog::error("LoadSettings: Cannot open file: {}", filename);
      errno = ENOENT;
      return false;
    }
    in.read(reinterpret_cast<char *>(&m_settings), sizeof(m_settings));
    in.close();
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start).count();
    spdlog::info("PS4Filesystem settings loaded from: {}", filename);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("LoadSettings failed: {}", e.what());
    errno = EIO;
    return false;
  }
}

bool PS4Filesystem::LoadGame(const std::string &gamePath) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_mutex);
  try {
    m_loadedGamePath = gamePath;
    m_gameLoaded = !gamePath.empty();
    m_stats.operationCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start).count();
    spdlog::info("LoadGame: Loaded {}", gamePath);
    return m_gameLoaded;
  } catch (const std::exception &e) {
    spdlog::error("LoadGame failed: {}", e.what());
    m_stats.cacheMisses++;
    return false;
  }
}

bool PS4Filesystem::StartGame() {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_mutex);
  try {
    if (!m_gameLoaded || m_loadedGamePath.empty()) {
      spdlog::error("StartGame: No game loaded");
      return false;
    }
    spdlog::info("Starting game: {}", m_loadedGamePath);
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start).count();
    return true;
  } catch (const std::exception &e) {
    spdlog::error("StartGame failed: {}", e.what());
    return false;
  }
}

std::string PS4Filesystem::GetLoadedGamePath() const {
  std::shared_lock<std::shared_mutex> lock(m_mutex);
  try {
    return m_gameLoaded ? m_loadedGamePath : "";
  } catch (const std::exception &e) {
    spdlog::error("GetLoadedGamePath failed: {}", e.what());
    return "";
  }
}

bool PS4Filesystem::IsGameLoaded() const {
  std::shared_lock<std::shared_mutex> lock(m_mutex);
  try {
    return m_gameLoaded;
  } catch (const std::exception &e) {
    spdlog::error("IsGameLoaded failed: {}", e.what());
    return false;
  }
}

std::string PS4Filesystem::GetGameDirectory() const {
  std::shared_lock<std::shared_mutex> lock(m_mutex);
  try {
    return m_gameDirectory;
  } catch (const std::exception &e) {
    spdlog::error("GetGameDirectory failed: {}", e.what());
    return "";
  }
}

void PS4Filesystem::SetGameDirectory(const std::string &path) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_mutex);
  try {
    m_gameDirectory = path;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start).count();
    spdlog::info("Game directory set to: {}", path);
  } catch (const std::exception &e) {
    spdlog::error("SetGameDirectory failed: {}", e.what());
  }
}

void PS4Filesystem::InitializeDeviceFiles() {
  // Note: This method assumes the caller already holds the mutex
  CreateDeviceFileInternal("/dev/null", PS4FileType::Device);
  CreateDeviceFileInternal("/dev/zero", PS4FileType::Device);
  CreateDeviceFileInternal("/dev/random", PS4FileType::Device);
  CreateDeviceFileInternal("/dev/urandom", PS4FileType::Device);
  CreateDeviceFileInternal("/dev/console", PS4FileType::Device);
  CreateDeviceFileInternal("/dev/dipsw", PS4FileType::Device);
  CreateDeviceFileInternal("/dev/hid", PS4FileType::Device);
  CreateDeviceFileInternal("/dev/gc", PS4FileType::Device);
  CreateDeviceFileInternal("/dev/rng", PS4FileType::Device);
  spdlog::info("Initialized {} device files", m_deviceFiles.size());
}

bool PS4Filesystem::CreateDeviceFile(const std::string &path, PS4FileType deviceType) {
  std::unique_lock<std::shared_mutex> lock(m_mutex);
  return CreateDeviceFileInternal(path, deviceType);
}

bool PS4Filesystem::CreateDeviceFileInternal(const std::string &path, PS4FileType deviceType) {
  // Note: This method assumes the caller already holds the mutex
  try {
    m_deviceFiles[path] = deviceType;
    FileEntry &entry = m_files[path];
    entry.path = path;
    entry.hostPath = MapToHostPath(path);
    entry.fileType = deviceType;
    entry.isDir = false;
    entry.present = true;
    entry.size = 0;
    entry.mode = 0666;
    entry.ps4Permissions = 0666;
    entry.creationTime = std::time(nullptr);
    entry.modificationTime = entry.creationTime;
    entry.accessTime = entry.creationTime;
    spdlog::debug("Created device file: {}", path);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("Failed to create device file {}: {}", path, e.what());
    errno = EIO;
    return false;
  }
}

bool PS4Filesystem::HandleDeviceAccess(const std::string &path, void *buffer, size_t size, bool isWrite) {
  try {
    if (path == "/dev/null" || path == "/dev/zero") {
      if (isWrite) return true;
      std::memset(buffer, 0, size);
      return true;
    } else if (path == "/dev/random" || path == "/dev/urandom") {
      if (isWrite) return true;
      std::random_device rd;
      std::mt19937 gen(rd());
      std::uniform_int_distribution<int> dis(0, 255);
      uint8_t *buf = static_cast<uint8_t *>(buffer);
      for (size_t i = 0; i < size; ++i) {
        buf[i] = static_cast<uint8_t>(dis(gen));
      }
      return true;
    } else if (path == "/dev/stdout" || path == "/dev/stderr") {
      if (isWrite) {
        spdlog::info("Device {} write: {}", path, std::string(static_cast<char *>(buffer), size));
        return true;
      }
      errno = EIO;
      return false;
    } else if (path == "/dev/stdin") {
      if (!isWrite) {
        // TODO: Implement console input
        errno = EIO;
        return false;
      }
      return true;
    }
    spdlog::warn("Unsupported device access: {}", path);
    errno = ENXIO;
    return false;
  } catch (const std::exception &e) {
    spdlog::error("HandleDeviceAccess failed for {}: {}", path, e.what());
    errno = EIO;
    return false;
  }
}

PS4FileType PS4Filesystem::DetermineFileType(const std::string &path) const {
  try {
    if (path.empty()) return PS4FileType::Regular;
    if (path.back() == '/' || path.back() == '\\') return PS4FileType::Directory;
    if (m_deviceFiles.find(path) != m_deviceFiles.end()) return PS4FileType::Device;

    size_t dotPos = path.find_last_of('.');
    if (dotPos == std::string::npos) return PS4FileType::Regular;
    std::string ext = path.substr(dotPos + 1);
    std::transform(ext.begin(), ext.end(), ext.begin(), ::tolower);

    if (ext == "elf" || ext == "bin" || ext == "so" || ext == "dll" || ext == "sprx" || ext == "pkg") {
      return PS4FileType::Regular;
    } else if (ext == "sfo") {
      return PS4FileType::System;
    } else if (ext == "txt" || ext == "log") {
      return PS4FileType::Regular;
    }
    return PS4FileType::Regular;
  } catch (const std::exception &e) {
    spdlog::error("DetermineFileType failed for {}: {}", path, e.what());
    return PS4FileType::Regular;
  }
}

bool PS4Filesystem::ValidatePS4Permissions(const std::string &path, int mode) {
  try {
    std::shared_lock<std::shared_mutex> lock(m_mutex);
    auto it = m_files.find(path);
    if (it == m_files.end()) {
      errno = ENOENT;
      return false;
    }
    const FileEntry &entry = it->second;
    if ((mode & O_RDONLY) && !(entry.mode & 0444)) return false;
    if ((mode & O_WRONLY) && !(entry.mode & 0222)) return false;
#ifndef O_EXEC
#define O_EXEC 0x040000
#endif
    if ((mode & O_EXEC) && !(entry.mode & 0111)) return false;
    return true;
  } catch (const std::exception &e) {
    spdlog::error("ValidatePS4Permissions failed for {}: {}", path, e.what());
    errno = EACCES;
    return false;
  }
}

std::string PS4Filesystem::MapToHostPath(const std::string &virtualPath) {
  try {
    // Note: This method assumes the caller already holds the mutex
    std::filesystem::path hostPath = m_mountPoints.GetHostPath(virtualPath);
    spdlog::debug("MapToHostPath: virtualPath='{}', mountPoint result='{}'", virtualPath, hostPath.string());

    if (hostPath.empty()) {
      // Properly concatenate root path with virtual path using filesystem::path
      std::filesystem::path rootPath(m_rootPath);
      std::filesystem::path vPath(virtualPath);
      // Remove leading slash from virtual path if present to avoid double slashes
      std::string cleanVirtualPath = virtualPath;
      if (!cleanVirtualPath.empty() && cleanVirtualPath[0] == '/') {
        cleanVirtualPath = cleanVirtualPath.substr(1);
      }
      std::string result = (rootPath / cleanVirtualPath).string();
      spdlog::debug("MapToHostPath: using fallback, result='{}'", result);
      return result;
    }
    spdlog::debug("MapToHostPath: using mount point, result='{}'", hostPath.string());
    return hostPath.string();
  } catch (const std::exception &e) {
    spdlog::error("MapToHostPath failed for {}: {}", virtualPath, e.what());
    return virtualPath;
  }
}

std::string PS4Filesystem::ResolvePath(const std::string &virtualPath) const {
  if (virtualPath.empty()) return m_rootPath;
  std::string cleanPath = virtualPath[0] == '/' ? virtualPath.substr(1) : virtualPath;
  if (m_rootPath.empty()) {
    return cleanPath;
  }
  // Properly concatenate root path with clean path using filesystem::path
  std::filesystem::path rootPath(m_rootPath);
  return (rootPath / cleanPath).string();
}

std::vector<uint8_t> PS4Filesystem::CalculateChecksum(const std::vector<uint8_t> &data) const {
  try {
    uint32_t crc = 0xFFFFFFFF;
    const uint32_t polynomial = 0xEDB88320;
    for (uint8_t byte : data) {
      crc ^= byte;
      for (int i = 0; i < 8; ++i) {
        if (crc & 1) crc = (crc >> 1) ^ polynomial;
        else crc >>= 1;
      }
    }
    crc ^= 0xFFFFFFFF;
    std::vector<uint8_t> checksum(4);
    checksum[0] = (crc >> 24) & 0xFF;
    checksum[1] = (crc >> 16) & 0xFF;
    checksum[2] = (crc >> 8) & 0xFF;
    checksum[3] = crc & 0xFF;
    return checksum;
  } catch (const std::exception &e) {
    spdlog::error("Checksum calculation failed: {}", e.what());
    return {};
  }
}

bool PS4Filesystem::InitializePFS() {
  std::unique_lock<std::shared_mutex> lock(m_mutex);
  return InitializePFSInternal();
}

bool PS4Filesystem::InitializePFSInternal() {
  // Note: This method assumes the caller already holds the mutex
  try {
    spdlog::info("Creating standard directories... (root path: {})", m_rootPath);
    std::vector<std::string> standardDirs = {"/app0", "/system", "/user", "/tmp", "/dev", "/proc"};
    for (const auto &dir : standardDirs) {
      spdlog::debug("Processing directory: {}", dir);
      FileEntry &entry = m_files[dir];
      entry.path = dir;
      entry.hostPath = MapToHostPath(dir);
      spdlog::debug("Mapped {} to host path: {}", dir, entry.hostPath);
      entry.fileType = PS4FileType::Directory;
      entry.size = 0;
      entry.mode = 0755;
      entry.modificationTime = std::time(nullptr);
      entry.present = true;
      entry.isDir = true;
      m_directories.push_back(dir);
      spdlog::debug("Creating directory: {}", entry.hostPath);
      try {
        std::error_code ec;
        bool created = std::filesystem::create_directories(entry.hostPath, ec);
        if (ec) {
          spdlog::warn("Failed to create directory {}: {} ({})", entry.hostPath, ec.message(), ec.value());
        } else {
          spdlog::debug("Created directory: {} (created: {})", entry.hostPath, created);
        }
      } catch (const std::exception &e) {
        spdlog::error("Exception creating directory {}: {}", entry.hostPath, e.what());
      }
    }
    spdlog::info("Standard directories created");

    spdlog::info("Creating device files...");
    std::vector<std::string> deviceFiles = {"/dev/null", "/dev/zero", "/dev/random", "/dev/urandom"};
    for (const auto &dev : deviceFiles) {
      spdlog::debug("Creating device file: {}", dev);
      CreateDeviceFileInternal(dev, PS4FileType::Device);
    }
    spdlog::info("Device files created");

    spdlog::info("Initializing PFS block management...");
    // Initialize PFS block management (stubbed)
    for (auto &entry : m_files) {
      if (entry.second.fileType == PS4FileType::PFS) {
        entry.second.blockSize = m_settings.pfsBlockSize;
        // TODO: Implement block allocation and encryption
      }
    }
    spdlog::info("PFS block management initialized");

    spdlog::info("PS4 Filesystem initialized successfully");
    return true;
  } catch (const std::exception &e) {
    spdlog::error("InitializePFS failed: {}", e.what());
    errno = EIO;
    return false;
  }
}

bool PS4Filesystem::EncryptFile(const std::string &path, const std::vector<uint8_t> &key) {
  try {
    std::unique_lock<std::shared_mutex> lock(m_mutex);
    auto it = m_files.find(path);
    if (it == m_files.end()) {
      spdlog::error("EncryptFile: File not found: {}", path);
      errno = ENOENT;
      return false;
    }

    FileEntry &entry = it->second;
    if (entry.data.empty()) return true;

    // AES-256-CBC encryption (placeholder)
    if (key.size() != 32) {
      spdlog::error("EncryptFile: Invalid key size for {}", path);
      errno = EINVAL;
      return false;
    }
    std::vector<uint8_t> iv(16, 0); // Stubbed IV
    AES_KEY aesKey;
    AES_set_encrypt_key(key.data(), 256, &aesKey);
    std::vector<uint8_t> encrypted(entry.data.size());
    AES_cbc_encrypt(entry.data.data(), encrypted.data(), entry.data.size(), &aesKey, iv.data(), AES_ENCRYPT);

    entry.data = std::move(encrypted);
    entry.isEncrypted = true;
    entry.encryptionKey = key;
    spdlog::debug("Encrypted file: {}", path);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("EncryptFile failed for {}: {}", path, e.what());
    errno = EIO;
    return false;
  }
}

bool PS4Filesystem::DecryptFile(const std::string &path) {
  try {
    std::unique_lock<std::shared_mutex> lock(m_mutex);
    auto it = m_files.find(path);
    if (it == m_files.end()) {
      spdlog::error("DecryptFile: File not found: {}", path);
      errno = ENOENT;
      return false;
    }

    FileEntry &entry = it->second;
    if (!entry.isEncrypted || entry.encryptionKey.empty()) return true;

    // AES-256-CBC decryption (placeholder)
    std::vector<uint8_t> iv(16, 0); // Stubbed IV
    AES_KEY aesKey;
    AES_set_decrypt_key(entry.encryptionKey.data(), 256, &aesKey);
    std::vector<uint8_t> decrypted(entry.data.size());
    AES_cbc_encrypt(entry.data.data(), decrypted.data(), entry.data.size(), &aesKey, iv.data(), AES_DECRYPT);

    entry.data = std::move(decrypted);
    entry.isEncrypted = false;
    entry.encryptionKey.clear();
    spdlog::debug("Decrypted file: {}", path);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("DecryptFile failed for {}: {}", path, e.what());
    errno = EIO;
    return false;
  }
}

// MntPoints implementation
void MntPoints::Mount(const std::filesystem::path &host_folder, const std::string &guest_folder,
                      bool read_only) {
  std::scoped_lock lock{m_mutex};
  std::string sanitized = guest_folder;
  while (sanitized.ends_with("/")) {
    sanitized.pop_back();
  }
  m_mount_pairs.emplace_back(MntPair{host_folder, sanitized, read_only});
  spdlog::info("Mounted {} to {}", host_folder.string(), sanitized);
}

void MntPoints::Unmount(const std::string &guest_folder) {
  std::scoped_lock lock{m_mutex};
  std::string sanitized = guest_folder;
  while (sanitized.ends_with("/")) {
    sanitized.pop_back();
  }
  auto it = std::remove_if(m_mount_pairs.begin(), m_mount_pairs.end(),
                           [&](const MntPair &pair) { return pair.mount == sanitized; });
  m_mount_pairs.erase(it, m_mount_pairs.end());
  spdlog::info("Unmounted {}", sanitized);
}

void MntPoints::UnmountAll() {
  std::scoped_lock lock{m_mutex};
  m_mount_pairs.clear();
  path_cache.clear();
  spdlog::info("Unmounted all mount points");
}

std::filesystem::path MntPoints::GetHostPath(const std::string &guest_path, bool *is_read_only) {
  std::scoped_lock lock{m_mutex};
  std::string corrected_path = guest_path;
  while (corrected_path.find("//") != std::string::npos) {
    corrected_path.replace(corrected_path.find("//"), 2, "/");
  }
  while (corrected_path.ends_with("/")) {
    corrected_path.pop_back();
  }

  auto it = std::find_if(m_mount_pairs.begin(), m_mount_pairs.end(),
                         [&](const MntPair &pair) {
                           return corrected_path == pair.mount ||
                                  corrected_path.starts_with(pair.mount + "/");
                         });
  if (it == m_mount_pairs.end()) {
    return "";
  }

  if (is_read_only) {
    *is_read_only = it->read_only;
  }

  if (corrected_path == it->mount) {
    return it->host_path;
  }

  std::string rel_path = corrected_path.substr(it->mount.size());
  if (!rel_path.empty() && rel_path[0] == '/') {
    rel_path = rel_path.substr(1);
  }

  std::filesystem::path host_path = it->host_path / rel_path;
  std::filesystem::path patch_path = it->host_path;
  patch_path += "-patch";
  patch_path /= rel_path;

  if (corrected_path.starts_with("/app0") && std::filesystem::exists(patch_path)) {
    return patch_path;
  }

#ifdef _WIN32
  // Case-insensitive path resolution for Windows
  path_parts.clear();
  auto current_path = host_path;
  while (!std::filesystem::exists(current_path)) {
    if (auto cache_it = path_cache.find(current_path.string()); cache_it != path_cache.end()) {
      current_path = cache_it->second;
      break;
    }
    path_parts.emplace_back(current_path.filename());
    current_path = current_path.parent_path();
  }

  auto guest_path_copy = corrected_path;
  auto guest_path_current = it->mount;
  while (!path_parts.empty()) {
    auto part = path_parts.back();
    auto add_match = [&](const auto &host_part) {
      current_path /= host_part;
      guest_path_current += "/" + part.string();
      path_cache[guest_path_current] = current_path;
      path_parts.pop_back();
    };

    if (std::filesystem::exists(current_path / part)) {
      add_match(part);
      continue;
    }

    std::string part_lower = part.string();
    std::transform(part_lower.begin(), part_lower.end(), part_lower.begin(), ::tolower);
    bool found = false;
    for (const auto &entry : std::filesystem::directory_iterator(current_path)) {
      std::string candidate = entry.path().filename().string();
      std::string candidate_lower = candidate;
      std::transform(candidate_lower.begin(), candidate_lower.end(), candidate_lower.begin(), ::tolower);
      if (candidate_lower == part_lower) {
        add_match(entry.path().filename());
        found = true;
        break;
      }
    }
    if (!found) {
      return host_path; // Fallback to original path
    }
  }
  return current_path;
#else
  return host_path;
#endif
}

void MntPoints::IterateDirectory(const std::string &guest_directory, const IterateDirectoryCallback &callback) {
  std::filesystem::path base_path = GetHostPath(guest_directory, nullptr);
  std::filesystem::path patch_path = base_path;
  patch_path += "-patch";
  bool apply_patch = std::filesystem::exists(patch_path) && patch_path != base_path;

  if (std::filesystem::exists(base_path)) {
    for (const auto &entry : std::filesystem::directory_iterator(base_path)) {
      if (apply_patch) {
        auto patch_entry_path = patch_path / entry.path().filename();
        if (std::filesystem::exists(patch_entry_path)) {
          callback(patch_entry_path, !std::filesystem::is_directory(patch_entry_path));
          continue;
        }
      }
      callback(entry.path(), !entry.is_directory());
    }
  }

  if (apply_patch) {
    for (const auto &entry : std::filesystem::directory_iterator(patch_path)) {
      auto base_entry_path = base_path / entry.path().filename();
      if (!std::filesystem::exists(base_entry_path)) {
        callback(entry.path(), !entry.is_directory());
      }
    }
  }
}

// HandleTable implementation
int HandleTable::CreateHandle() {
  std::scoped_lock lock{m_mutex};
  auto *file = new File{};
  file->is_opened = false;

  int existingFilesNum = m_files.size();
  for (int index = 0; index < existingFilesNum; index++) {
    if (m_files.at(index) == nullptr) {
      m_files[index] = file;
      return index;
    }
  }

  m_files.push_back(file);
  return m_files.size() - 1;
}

void HandleTable::DeleteHandle(int fd) {
  std::scoped_lock lock{m_mutex};
  if (fd >= 0 && fd < static_cast<int>(m_files.size())) {
    delete m_files[fd];
    m_files[fd] = nullptr;
  }
}

HandleTable::File *HandleTable::GetFile(int fd) {
  std::scoped_lock lock{m_mutex};
  if (fd < 0 || fd >= static_cast<int>(m_files.size())) {
    return nullptr;
  }
  return m_files[fd];
}

HandleTable::File *HandleTable::GetFile(const std::filesystem::path &host_name) {
  std::scoped_lock lock{m_mutex};
  for (auto *file : m_files) {
    if (file != nullptr && file->host_name == host_name) {
      return file;
    }
  }
  return nullptr;
}

void HandleTable::CreateStdHandles() {
  int fd = CreateHandle();
  auto *file = GetFile(fd);
  file->is_opened = true;
  file->type = PS4FileType::Device;
  file->guest_name = "/dev/stdin";

  fd = CreateHandle();
  file = GetFile(fd);
  file->is_opened = true;
  file->type = PS4FileType::Device;
  file->guest_name = "/dev/stdout";

  fd = CreateHandle();
  file = GetFile(fd);
  file->is_opened = true;
  file->type = PS4FileType::Device;
  file->guest_name = "/dev/stderr";
}

} // namespace ps4